/**
 * Post Model
 * Business entity cho Post (Blog)
 */

import { BaseEntity, Status, SEOData } from "./common.model";

/**
 * Post Entity
 */
export interface PostEntity extends BaseEntity {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  status: Status;
  authorId: string;
  categoryId?: string;
  featuredImage?: string;
  publishedAt?: Date;
  tags: string[];
  viewCount: number;
  commentCount: number;
  seo?: SEOData;
  metadata?: Record<string, any>;
}

/**
 * Post with Relations
 */
export interface PostWithRelations extends PostEntity {
  author?: any; // UserEntity
  category?: any; // CategoryEntity
}

/**
 * Create Post Data
 */
export interface CreatePostData {
  title: string;
  slug?: string;
  content: string;
  excerpt?: string;
  status?: Status;
  authorId: string;
  categoryId?: string;
  featuredImage?: string;
  publishedAt?: Date;
  tags?: string[];
  seo?: SEOData;
}

/**
 * Update Post Data
 */
export interface UpdatePostData {
  title?: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  status?: Status;
  categoryId?: string;
  featuredImage?: string;
  publishedAt?: Date;
  tags?: string[];
  seo?: SEOData;
}

/**
 * Post Business Rules
 */
export class PostBusinessRules {
  static validatePost(data: CreatePostData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.title || data.title.trim().length === 0) {
      errors.push("Title is required");
    }

    if (!data.content || data.content.trim().length === 0) {
      errors.push("Content is required");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  static validateSlug(slug: string): boolean {
    const slugRegex = /^[a-z0-9-]+$/;
    return slugRegex.test(slug) && slug.length >= 2 && slug.length <= 100;
  }

  static extractExcerpt(content: string, maxLength: number = 160): string {
    const plainText = content.replace(/<[^>]*>/g, "");
    return plainText.length > maxLength
      ? plainText.substring(0, maxLength) + "..."
      : plainText;
  }

  static canPublish(post: PostEntity, user: any): boolean {
    return (
      post.authorId === user.id ||
      user.role === "ADMIN" ||
      user.role === "SUPER_ADMIN"
    );
  }
}
