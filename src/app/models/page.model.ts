/**
 * Page Model
 * Business entity cho Page
 */

import { BaseEntity, SEOData } from "./common.model";
import { PageStatus } from "@prisma/client";

/**
 * Page Entity
 */
export interface PageEntity extends BaseEntity {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  status: PageStatus;
  template?: string;
  parentId?: string;
  sortOrder: number;
  isHomepage: boolean;
  seo?: SEOData;
  metadata?: Record<string, any>;
}

/**
 * Page with Relations
 */
export interface PageWithRelations extends PageEntity {
  parent?: PageEntity;
  children?: PageEntity[];
}

/**
 * Create Page Data
 */
export interface CreatePageData {
  title: string;
  slug?: string;
  content: string;
  excerpt?: string;
  status?: PageStatus;
  template?: string;
  parentId?: string;
  sortOrder?: number;
  isHomepage?: boolean;
  seo?: SEOData;
}

/**
 * Update Page Data
 */
export interface UpdatePageData {
  title?: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  status?: PageStatus;
  template?: string;
  parentId?: string;
  sortOrder?: number;
  isHomepage?: boolean;
  seo?: SEOData;
}

/**
 * Page Business Rules
 */
export class PageBusinessRules {
  static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  static validateSlug(slug: string): boolean {
    const slugRegex = /^[a-z0-9-]+$/;
    return slugRegex.test(slug) && slug.length >= 2 && slug.length <= 100;
  }

  static extractExcerpt(content: string, maxLength: number = 160): string {
    const plainText = content.replace(/<[^>]*>/g, "");
    return plainText.length > maxLength
      ? plainText.substring(0, maxLength) + "..."
      : plainText;
  }

  static validatePage(data: CreatePageData | UpdatePageData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate title
    if ("title" in data && data.title !== undefined) {
      if (!data.title?.trim()) {
        errors.push("Page title is required");
      } else if (data.title.trim().length < 2) {
        errors.push("Page title must be at least 2 characters");
      } else if (data.title.trim().length > 200) {
        errors.push("Page title must not exceed 200 characters");
      }
    }

    // Validate slug
    if ("slug" in data && data.slug !== undefined) {
      if (!data.slug?.trim()) {
        errors.push("Page slug is required");
      } else if (!/^[a-z0-9-]+$/.test(data.slug)) {
        errors.push(
          "Page slug can only contain lowercase letters, numbers, and hyphens"
        );
      } else if (data.slug.length < 2) {
        errors.push("Page slug must be at least 2 characters");
      } else if (data.slug.length > 100) {
        errors.push("Page slug must not exceed 100 characters");
      }
    }

    // Validate content
    if ("content" in data && data.content !== undefined) {
      if (!data.content?.trim()) {
        errors.push("Page content is required");
      } else if (data.content.trim().length < 10) {
        errors.push("Page content must be at least 10 characters");
      }
    }

    // Validate meta description
    if ("metaDescription" in data && data.metaDescription) {
      if (data.metaDescription.length > 160) {
        errors.push("Meta description must not exceed 160 characters");
      }
    }

    // Validate meta keywords
    if ("metaKeywords" in data && data.metaKeywords) {
      if (data.metaKeywords.length > 255) {
        errors.push("Meta keywords must not exceed 255 characters");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static canDelete(page: PageEntity): {
    canDelete: boolean;
    reason?: string;
  } {
    // Check if it's a system page that shouldn't be deleted
    const systemPages = ["home", "about", "contact", "privacy", "terms"];

    if (systemPages.includes(page.slug)) {
      return {
        canDelete: false,
        reason: "Cannot delete system pages",
      };
    }

    return { canDelete: true };
  }

  static getStatusLabel(status: Status): string {
    const statusLabels: Record<Status, string> = {
      [Status.ACTIVE]: "Published",
      [Status.INACTIVE]: "Draft",
    };

    return statusLabels[status] || status;
  }

  static generateMetaDescription(content: string): string {
    const plainText = content.replace(/<[^>]*>/g, "");
    const sentences = plainText.split(/[.!?]+/);
    let description = "";

    for (const sentence of sentences) {
      const trimmed = sentence.trim();
      if (trimmed && (description + trimmed).length <= 160) {
        description += (description ? ". " : "") + trimmed;
      } else {
        break;
      }
    }

    return description || this.extractExcerpt(content, 160);
  }

  static validateSlugUniqueness(slug: string, excludeId?: string): boolean {
    // This would typically check against database
    // For now, just validate format
    return /^[a-z0-9-]+$/.test(slug);
  }

  static getReadingTime(content: string): number {
    const plainText = content.replace(/<[^>]*>/g, "");
    const wordCount = plainText.split(/\s+/).length;
    const wordsPerMinute = 200; // Average reading speed

    return Math.ceil(wordCount / wordsPerMinute);
  }

  static isSystemPage(slug: string): boolean {
    const systemPages = ["home", "about", "contact", "privacy", "terms"];
    return systemPages.includes(slug);
  }

  static generateBreadcrumbs(
    page: PageEntity
  ): Array<{ title: string; url: string }> {
    const breadcrumbs = [{ title: "Home", url: "/" }];

    if (!this.isSystemPage(page.slug)) {
      breadcrumbs.push({ title: "Pages", url: "/pages" });
    }

    breadcrumbs.push({ title: page.title, url: `/pages/${page.slug}` });

    return breadcrumbs;
  }
}
