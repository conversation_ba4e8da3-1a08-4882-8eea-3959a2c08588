/**
 * Promotion Model
 * Business entity cho Promotion/Discount
 */

import { BaseEntity } from "./common.model";
import { PromotionType } from "@prisma/client";

/**
 * Promotion Entity
 */
export interface PromotionEntity extends BaseEntity {
  name: string;
  description?: string;
  type: PromotionType;
  value: number;
  isPercentage: boolean;
  minOrderValue?: number;
  maxDiscountValue?: number;
  usageLimit?: number;
  usageCount: number;
  userUsageLimit?: number;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  status: PromotionStatus;
  code?: string;
  applicableProducts: string[];
  excludedProducts: string[];
  applicableCategories: string[];
  excludedCategories: string[];
  createdBy: string;
  metadata?: Record<string, any>;
}

/**
 * Promotion Status
 */
export enum PromotionStatus {
  DRAFT = "DRAFT",
  ACTIVE = "ACTIVE",
  PAUSED = "PAUSED",
  EXPIRED = "EXPIRED",
  CANCELLED = "CANCELLED",
}

/**
 * Promotion with Relations
 */
export interface PromotionWithRelations extends PromotionEntity {
  usages?: PromotionUsageEntity[];
  creator?: any; // UserEntity
}

/**
 * Promotion Usage Entity
 */
export interface PromotionUsageEntity extends BaseEntity {
  promotionId: string;
  userId: string;
  orderId: string;
  discountAmount: number;
  usedAt: Date;
}

/**
 * Create Promotion Data
 */
export interface CreatePromotionData {
  name: string;
  description?: string;
  type: PromotionType;
  value: number;
  isPercentage?: boolean;
  minOrderValue?: number;
  maxDiscountValue?: number;
  usageLimit?: number;
  userUsageLimit?: number;
  startDate: Date;
  endDate: Date;
  code?: string;
  applicableProducts?: string[];
  excludedProducts?: string[];
  applicableCategories?: string[];
  excludedCategories?: string[];
  createdBy: string;
}

/**
 * Update Promotion Data
 */
export interface UpdatePromotionData {
  name?: string;
  description?: string;
  value?: number;
  isPercentage?: boolean;
  minOrderValue?: number;
  maxDiscountValue?: number;
  usageLimit?: number;
  userUsageLimit?: number;
  startDate?: Date;
  endDate?: Date;
  isActive?: boolean;
  status?: PromotionStatus;
  code?: string;
  applicableProducts?: string[];
  excludedProducts?: string[];
  applicableCategories?: string[];
  excludedCategories?: string[];
}

/**
 * Promotion Search Filters
 */
export interface PromotionSearchFilters extends SearchFilters {
  type?: PromotionType;
  status?: PromotionStatus;
  isActive?: boolean;
  startDate?: Date;
  endDate?: Date;
  createdBy?: string;
}

/**
 * Promotion Business Rules
 */
export class PromotionBusinessRules {
  static readonly MIN_DISCOUNT_VALUE = 0.01;
  static readonly MAX_PERCENTAGE_DISCOUNT = 100;
  static readonly MIN_PROMOTION_DURATION_HOURS = 1;
  static readonly MAX_PROMOTION_DURATION_DAYS = 365;

  static validatePromotionData(data: CreatePromotionData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate name
    if (!data.name || data.name.trim().length === 0) {
      errors.push("Promotion name is required");
    }

    if (data.name && data.name.length > 200) {
      errors.push("Promotion name must be less than 200 characters");
    }

    // Validate value
    if (data.value < this.MIN_DISCOUNT_VALUE) {
      errors.push(`Discount value must be at least ${this.MIN_DISCOUNT_VALUE}`);
    }

    if (data.isPercentage && data.value > this.MAX_PERCENTAGE_DISCOUNT) {
      errors.push(
        `Percentage discount cannot exceed ${this.MAX_PERCENTAGE_DISCOUNT}%`
      );
    }

    // Validate dates
    if (data.startDate >= data.endDate) {
      errors.push("End date must be after start date");
    }

    const durationMs = data.endDate.getTime() - data.startDate.getTime();
    const minDurationMs = this.MIN_PROMOTION_DURATION_HOURS * 60 * 60 * 1000;
    const maxDurationMs =
      this.MAX_PROMOTION_DURATION_DAYS * 24 * 60 * 60 * 1000;

    if (durationMs < minDurationMs) {
      errors.push(
        `Promotion duration must be at least ${this.MIN_PROMOTION_DURATION_HOURS} hour(s)`
      );
    }

    if (durationMs > maxDurationMs) {
      errors.push(
        `Promotion duration cannot exceed ${this.MAX_PROMOTION_DURATION_DAYS} days`
      );
    }

    // Validate usage limits
    if (data.usageLimit && data.usageLimit < 1) {
      errors.push("Usage limit must be at least 1");
    }

    if (data.userUsageLimit && data.userUsageLimit < 1) {
      errors.push("User usage limit must be at least 1");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static canUsePromotion(
    promotion: PromotionEntity,
    userId: string,
    orderValue: number,
    userUsageCount: number = 0
  ): { canUse: boolean; reason?: string } {
    // Check if promotion is active
    if (!promotion.isActive) {
      return { canUse: false, reason: "Promotion is not active" };
    }

    // Check status
    if (promotion.status !== PromotionStatus.ACTIVE) {
      return { canUse: false, reason: "Promotion is not available" };
    }

    // Check date range
    const now = new Date();
    if (now < promotion.startDate) {
      return { canUse: false, reason: "Promotion has not started yet" };
    }

    if (now > promotion.endDate) {
      return { canUse: false, reason: "Promotion has expired" };
    }

    // Check minimum order value
    if (promotion.minOrderValue && orderValue < promotion.minOrderValue) {
      return {
        canUse: false,
        reason: `Minimum order value is ${promotion.minOrderValue}`,
      };
    }

    // Check usage limits
    if (promotion.usageLimit && promotion.usageCount >= promotion.usageLimit) {
      return { canUse: false, reason: "Promotion usage limit reached" };
    }

    if (
      promotion.userUsageLimit &&
      userUsageCount >= promotion.userUsageLimit
    ) {
      return { canUse: false, reason: "User usage limit reached" };
    }

    return { canUse: true };
  }

  static calculateDiscount(
    promotion: PromotionEntity,
    orderValue: number
  ): number {
    let discount = 0;

    if (promotion.isPercentage) {
      discount = (orderValue * promotion.value) / 100;
    } else {
      discount = promotion.value;
    }

    // Apply maximum discount limit
    if (promotion.maxDiscountValue && discount > promotion.maxDiscountValue) {
      discount = promotion.maxDiscountValue;
    }

    // Ensure discount doesn't exceed order value
    if (discount > orderValue) {
      discount = orderValue;
    }

    return Math.round(discount * 100) / 100; // Round to 2 decimal places
  }

  static canDeletePromotion(promotion: PromotionEntity): {
    canDelete: boolean;
    reason?: string;
  } {
    // Cannot delete if promotion has been used
    if (promotion.usageCount > 0) {
      return {
        canDelete: false,
        reason: "Cannot delete promotion that has been used",
      };
    }

    // Cannot delete active promotions
    if (promotion.isActive && promotion.status === PromotionStatus.ACTIVE) {
      return {
        canDelete: false,
        reason: "Cannot delete active promotion",
      };
    }

    return { canDelete: true };
  }

  static getPromotionStatus(promotion: PromotionEntity): PromotionStatus {
    const now = new Date();

    if (!promotion.isActive) {
      return PromotionStatus.PAUSED;
    }

    if (now < promotion.startDate) {
      return PromotionStatus.DRAFT;
    }

    if (now > promotion.endDate) {
      return PromotionStatus.EXPIRED;
    }

    if (promotion.usageLimit && promotion.usageCount >= promotion.usageLimit) {
      return PromotionStatus.EXPIRED;
    }

    return PromotionStatus.ACTIVE;
  }
}
