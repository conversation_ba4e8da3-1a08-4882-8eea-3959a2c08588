# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@adobe/css-tools@^4.4.0":
  version "4.4.3"

"@alloc/quick-lru@^5.2.0":
  version "5.2.0"

"@ampproject/remapping@^2.2.0", "@ampproject/remapping@^2.3.0":
  version "2.3.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@asamuzakjp/css-color@^3.2.0":
  version "3.2.0"
  dependencies:
    "@csstools/css-calc" "^2.1.3"
    "@csstools/css-color-parser" "^3.0.9"
    "@csstools/css-parser-algorithms" "^3.0.4"
    "@csstools/css-tokenizer" "^3.0.3"
    lru-cache "^10.4.3"

"@aws-crypto/sha256-browser@5.2.0":
  version "5.2.0"
  dependencies:
    "@aws-crypto/sha256-js" "^5.2.0"
    "@aws-crypto/supports-web-crypto" "^5.2.0"
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@smithy/util-utf8" "^2.0.0"
    tslib "^2.6.2"

"@aws-crypto/sha256-js@^5.2.0", "@aws-crypto/sha256-js@5.2.0":
  version "5.2.0"
  dependencies:
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^2.6.2"

"@aws-crypto/supports-web-crypto@^5.2.0":
  version "5.2.0"
  dependencies:
    tslib "^2.6.2"

"@aws-crypto/util@^5.2.0":
  version "5.2.0"
  dependencies:
    "@aws-sdk/types" "^3.222.0"
    "@smithy/util-utf8" "^2.0.0"
    tslib "^2.6.2"

"@aws-sdk/client-ses@^3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/credential-provider-node" "3.848.0"
    "@aws-sdk/middleware-host-header" "3.840.0"
    "@aws-sdk/middleware-logger" "3.840.0"
    "@aws-sdk/middleware-recursion-detection" "3.840.0"
    "@aws-sdk/middleware-user-agent" "3.848.0"
    "@aws-sdk/region-config-resolver" "3.840.0"
    "@aws-sdk/types" "3.840.0"
    "@aws-sdk/util-endpoints" "3.848.0"
    "@aws-sdk/util-user-agent-browser" "3.840.0"
    "@aws-sdk/util-user-agent-node" "3.848.0"
    "@smithy/config-resolver" "^4.1.4"
    "@smithy/core" "^3.7.0"
    "@smithy/fetch-http-handler" "^5.1.0"
    "@smithy/hash-node" "^4.0.4"
    "@smithy/invalid-dependency" "^4.0.4"
    "@smithy/middleware-content-length" "^4.0.4"
    "@smithy/middleware-endpoint" "^4.1.15"
    "@smithy/middleware-retry" "^4.1.16"
    "@smithy/middleware-serde" "^4.0.8"
    "@smithy/middleware-stack" "^4.0.4"
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/node-http-handler" "^4.1.0"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/smithy-client" "^4.4.7"
    "@smithy/types" "^4.3.1"
    "@smithy/url-parser" "^4.0.4"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-body-length-node" "^4.0.0"
    "@smithy/util-defaults-mode-browser" "^4.0.23"
    "@smithy/util-defaults-mode-node" "^4.0.23"
    "@smithy/util-endpoints" "^3.0.6"
    "@smithy/util-middleware" "^4.0.4"
    "@smithy/util-retry" "^4.0.6"
    "@smithy/util-utf8" "^4.0.0"
    "@smithy/util-waiter" "^4.0.6"
    tslib "^2.6.2"

"@aws-sdk/client-sso@3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/middleware-host-header" "3.840.0"
    "@aws-sdk/middleware-logger" "3.840.0"
    "@aws-sdk/middleware-recursion-detection" "3.840.0"
    "@aws-sdk/middleware-user-agent" "3.848.0"
    "@aws-sdk/region-config-resolver" "3.840.0"
    "@aws-sdk/types" "3.840.0"
    "@aws-sdk/util-endpoints" "3.848.0"
    "@aws-sdk/util-user-agent-browser" "3.840.0"
    "@aws-sdk/util-user-agent-node" "3.848.0"
    "@smithy/config-resolver" "^4.1.4"
    "@smithy/core" "^3.7.0"
    "@smithy/fetch-http-handler" "^5.1.0"
    "@smithy/hash-node" "^4.0.4"
    "@smithy/invalid-dependency" "^4.0.4"
    "@smithy/middleware-content-length" "^4.0.4"
    "@smithy/middleware-endpoint" "^4.1.15"
    "@smithy/middleware-retry" "^4.1.16"
    "@smithy/middleware-serde" "^4.0.8"
    "@smithy/middleware-stack" "^4.0.4"
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/node-http-handler" "^4.1.0"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/smithy-client" "^4.4.7"
    "@smithy/types" "^4.3.1"
    "@smithy/url-parser" "^4.0.4"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-body-length-node" "^4.0.0"
    "@smithy/util-defaults-mode-browser" "^4.0.23"
    "@smithy/util-defaults-mode-node" "^4.0.23"
    "@smithy/util-endpoints" "^3.0.6"
    "@smithy/util-middleware" "^4.0.4"
    "@smithy/util-retry" "^4.0.6"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/core@3.846.0":
  version "3.846.0"
  dependencies:
    "@aws-sdk/types" "3.840.0"
    "@aws-sdk/xml-builder" "3.821.0"
    "@smithy/core" "^3.7.0"
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/signature-v4" "^5.1.2"
    "@smithy/smithy-client" "^4.4.7"
    "@smithy/types" "^4.3.1"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-middleware" "^4.0.4"
    "@smithy/util-utf8" "^4.0.0"
    fast-xml-parser "5.2.5"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-env@3.846.0":
  version "3.846.0"
  dependencies:
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/types" "3.840.0"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-http@3.846.0":
  version "3.846.0"
  dependencies:
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/types" "3.840.0"
    "@smithy/fetch-http-handler" "^5.1.0"
    "@smithy/node-http-handler" "^4.1.0"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/smithy-client" "^4.4.7"
    "@smithy/types" "^4.3.1"
    "@smithy/util-stream" "^4.2.3"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-ini@3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/credential-provider-env" "3.846.0"
    "@aws-sdk/credential-provider-http" "3.846.0"
    "@aws-sdk/credential-provider-process" "3.846.0"
    "@aws-sdk/credential-provider-sso" "3.848.0"
    "@aws-sdk/credential-provider-web-identity" "3.848.0"
    "@aws-sdk/nested-clients" "3.848.0"
    "@aws-sdk/types" "3.840.0"
    "@smithy/credential-provider-imds" "^4.0.6"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/shared-ini-file-loader" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-node@3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-sdk/credential-provider-env" "3.846.0"
    "@aws-sdk/credential-provider-http" "3.846.0"
    "@aws-sdk/credential-provider-ini" "3.848.0"
    "@aws-sdk/credential-provider-process" "3.846.0"
    "@aws-sdk/credential-provider-sso" "3.848.0"
    "@aws-sdk/credential-provider-web-identity" "3.848.0"
    "@aws-sdk/types" "3.840.0"
    "@smithy/credential-provider-imds" "^4.0.6"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/shared-ini-file-loader" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-process@3.846.0":
  version "3.846.0"
  dependencies:
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/types" "3.840.0"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/shared-ini-file-loader" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-sso@3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-sdk/client-sso" "3.848.0"
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/token-providers" "3.848.0"
    "@aws-sdk/types" "3.840.0"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/shared-ini-file-loader" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-web-identity@3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/nested-clients" "3.848.0"
    "@aws-sdk/types" "3.840.0"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/middleware-host-header@3.840.0":
  version "3.840.0"
  dependencies:
    "@aws-sdk/types" "3.840.0"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/middleware-logger@3.840.0":
  version "3.840.0"
  dependencies:
    "@aws-sdk/types" "3.840.0"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/middleware-recursion-detection@3.840.0":
  version "3.840.0"
  dependencies:
    "@aws-sdk/types" "3.840.0"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/middleware-user-agent@3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/types" "3.840.0"
    "@aws-sdk/util-endpoints" "3.848.0"
    "@smithy/core" "^3.7.0"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/nested-clients@3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/middleware-host-header" "3.840.0"
    "@aws-sdk/middleware-logger" "3.840.0"
    "@aws-sdk/middleware-recursion-detection" "3.840.0"
    "@aws-sdk/middleware-user-agent" "3.848.0"
    "@aws-sdk/region-config-resolver" "3.840.0"
    "@aws-sdk/types" "3.840.0"
    "@aws-sdk/util-endpoints" "3.848.0"
    "@aws-sdk/util-user-agent-browser" "3.840.0"
    "@aws-sdk/util-user-agent-node" "3.848.0"
    "@smithy/config-resolver" "^4.1.4"
    "@smithy/core" "^3.7.0"
    "@smithy/fetch-http-handler" "^5.1.0"
    "@smithy/hash-node" "^4.0.4"
    "@smithy/invalid-dependency" "^4.0.4"
    "@smithy/middleware-content-length" "^4.0.4"
    "@smithy/middleware-endpoint" "^4.1.15"
    "@smithy/middleware-retry" "^4.1.16"
    "@smithy/middleware-serde" "^4.0.8"
    "@smithy/middleware-stack" "^4.0.4"
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/node-http-handler" "^4.1.0"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/smithy-client" "^4.4.7"
    "@smithy/types" "^4.3.1"
    "@smithy/url-parser" "^4.0.4"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-body-length-node" "^4.0.0"
    "@smithy/util-defaults-mode-browser" "^4.0.23"
    "@smithy/util-defaults-mode-node" "^4.0.23"
    "@smithy/util-endpoints" "^3.0.6"
    "@smithy/util-middleware" "^4.0.4"
    "@smithy/util-retry" "^4.0.6"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/region-config-resolver@3.840.0":
  version "3.840.0"
  dependencies:
    "@aws-sdk/types" "3.840.0"
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/types" "^4.3.1"
    "@smithy/util-config-provider" "^4.0.0"
    "@smithy/util-middleware" "^4.0.4"
    tslib "^2.6.2"

"@aws-sdk/token-providers@3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-sdk/core" "3.846.0"
    "@aws-sdk/nested-clients" "3.848.0"
    "@aws-sdk/types" "3.840.0"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/shared-ini-file-loader" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/types@^3.222.0", "@aws-sdk/types@3.840.0":
  version "3.840.0"
  dependencies:
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/util-endpoints@3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-sdk/types" "3.840.0"
    "@smithy/types" "^4.3.1"
    "@smithy/url-parser" "^4.0.4"
    "@smithy/util-endpoints" "^3.0.6"
    tslib "^2.6.2"

"@aws-sdk/util-locate-window@^3.0.0":
  version "3.804.0"
  dependencies:
    tslib "^2.6.2"

"@aws-sdk/util-user-agent-browser@3.840.0":
  version "3.840.0"
  dependencies:
    "@aws-sdk/types" "3.840.0"
    "@smithy/types" "^4.3.1"
    bowser "^2.11.0"
    tslib "^2.6.2"

"@aws-sdk/util-user-agent-node@3.848.0":
  version "3.848.0"
  dependencies:
    "@aws-sdk/middleware-user-agent" "3.848.0"
    "@aws-sdk/types" "3.840.0"
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/xml-builder@3.821.0":
  version "3.821.0"
  dependencies:
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.27.2":
  version "7.27.7"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.11.0", "@babel/core@^7.23.9", "@babel/core@^7.27.4", "@babel/core@>=7.0.0-beta.0 <8":
  version "7.27.7"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.5"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.27.3"
    "@babel/helpers" "^7.27.6"
    "@babel/parser" "^7.27.7"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.27.7"
    "@babel/types" "^7.27.7"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.27.5":
  version "7.27.5"
  dependencies:
    "@babel/parser" "^7.27.5"
    "@babel/types" "^7.27.3"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.3":
  version "7.27.3"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.3"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0":
  version "7.27.1"

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"

"@babel/helpers@^7.27.6":
  version "7.27.6"
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.6"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.23.9", "@babel/parser@^7.27.2", "@babel/parser@^7.27.5", "@babel/parser@^7.27.7":
  version "7.27.7"
  dependencies:
    "@babel/types" "^7.27.7"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-import-attributes@^7.24.7":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/runtime@^7.12.5", "@babel/runtime@^7.20.13", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.7":
  version "7.27.6"

"@babel/template@^7.27.2":
  version "7.27.2"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1", "@babel/traverse@^7.27.3", "@babel/traverse@^7.27.7":
  version "7.27.7"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.5"
    "@babel/parser" "^7.27.7"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.7"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.27.1", "@babel/types@^7.27.3", "@babel/types@^7.27.6", "@babel/types@^7.27.7":
  version "7.27.7"
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"

"@bundled-es-modules/cookie@^2.0.1":
  version "2.0.1"
  dependencies:
    cookie "^0.7.2"

"@bundled-es-modules/statuses@^1.0.1":
  version "1.0.1"
  dependencies:
    statuses "^2.0.1"

"@bundled-es-modules/tough-cookie@^0.1.6":
  version "0.1.6"
  dependencies:
    "@types/tough-cookie" "^4.0.5"
    tough-cookie "^4.1.4"

"@csstools/color-helpers@^5.0.2":
  version "5.0.2"

"@csstools/css-calc@^2.1.3", "@csstools/css-calc@^2.1.4":
  version "2.1.4"

"@csstools/css-color-parser@^3.0.9":
  version "3.0.10"
  dependencies:
    "@csstools/color-helpers" "^5.0.2"
    "@csstools/css-calc" "^2.1.4"

"@csstools/css-parser-algorithms@^3.0.4", "@csstools/css-parser-algorithms@^3.0.5":
  version "3.0.5"

"@csstools/css-tokenizer@^3.0.3", "@csstools/css-tokenizer@^3.0.4":
  version "3.0.4"

"@esbuild/darwin-arm64@0.25.5":
  version "0.25.5"

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.7.0":
  version "4.7.0"
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.12.1":
  version "4.12.1"

"@eslint/config-array@^0.21.0":
  version "0.21.0"
  dependencies:
    "@eslint/object-schema" "^2.1.6"
    debug "^4.3.1"
    minimatch "^3.1.2"

"@eslint/config-helpers@^0.3.0":
  version "0.3.0"

"@eslint/core@^0.15.0", "@eslint/core@^0.15.1":
  version "0.15.1"
  resolved "https://registry.npmjs.org/@eslint/core/-/core-0.15.1.tgz"
  integrity sha512-bkOp+iumZCCbt1K1CmWf0R9pM5yKpDv+ZXtvSyQpudrI9kuFLp+bM2WOPXImuD/ceQuaa8f5pj93Y7zyECIGNA==
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/eslintrc@^3", "@eslint/eslintrc@^3.3.1":
  version "3.3.1"
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^10.0.1"
    globals "^14.0.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@9.32.0":
  version "9.32.0"

"@eslint/object-schema@^2.1.6":
  version "2.1.6"

"@eslint/plugin-kit@^0.3.4":
  version "0.3.4"
  resolved "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.3.4.tgz"
  integrity sha512-Ul5l+lHEcw3L5+k8POx6r74mxEYKG5kOb6Xpy2gCRW6zweT6TEhAf8vhxGgjhqrd/VO/Dirhsb+1hNpD1ue9hw==
  dependencies:
    "@eslint/core" "^0.15.1"
    levn "^0.4.1"

"@faker-js/faker@^9.9.0":
  version "9.9.0"

"@floating-ui/core@^1.7.2":
  version "1.7.2"
  dependencies:
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/dom@^1.0.0", "@floating-ui/dom@^1.7.2":
  version "1.7.2"
  dependencies:
    "@floating-ui/core" "^1.7.2"
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/react-dom@^2.0.0":
  version "2.1.4"
  dependencies:
    "@floating-ui/dom" "^1.7.2"

"@floating-ui/utils@^0.2.10":
  version "0.2.10"

"@hookform/resolvers@^4.1.3":
  version "4.1.3"
  dependencies:
    "@standard-schema/utils" "^0.3.0"

"@humanfs/core@^0.19.1":
  version "0.19.1"

"@humanfs/node@^0.16.6":
  version "0.16.6"
  dependencies:
    "@humanfs/core" "^0.19.1"
    "@humanwhocodes/retry" "^0.3.0"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"

"@humanwhocodes/retry@^0.3.0":
  version "0.3.1"

"@humanwhocodes/retry@^0.4.2":
  version "0.4.3"

"@img/sharp-darwin-arm64@0.34.3":
  version "0.34.3"
  optionalDependencies:
    "@img/sharp-libvips-darwin-arm64" "1.2.0"

"@img/sharp-libvips-darwin-arm64@1.2.0":
  version "1.2.0"

"@inquirer/checkbox@^4.1.9":
  version "4.1.9"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/figures" "^1.0.12"
    "@inquirer/type" "^3.0.7"
    ansi-escapes "^4.3.2"
    yoctocolors-cjs "^2.1.2"

"@inquirer/confirm@^5.0.0", "@inquirer/confirm@^5.1.13":
  version "5.1.13"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/type" "^3.0.7"

"@inquirer/core@^10.1.14":
  version "10.1.14"
  dependencies:
    "@inquirer/figures" "^1.0.12"
    "@inquirer/type" "^3.0.7"
    ansi-escapes "^4.3.2"
    cli-width "^4.1.0"
    mute-stream "^2.0.0"
    signal-exit "^4.1.0"
    wrap-ansi "^6.2.0"
    yoctocolors-cjs "^2.1.2"

"@inquirer/editor@^4.2.14":
  version "4.2.14"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/type" "^3.0.7"
    external-editor "^3.1.0"

"@inquirer/expand@^4.0.16":
  version "4.0.16"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/type" "^3.0.7"
    yoctocolors-cjs "^2.1.2"

"@inquirer/figures@^1.0.12":
  version "1.0.12"

"@inquirer/input@^4.2.0":
  version "4.2.0"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/type" "^3.0.7"

"@inquirer/number@^3.0.16":
  version "3.0.16"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/type" "^3.0.7"

"@inquirer/password@^4.0.16":
  version "4.0.16"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/type" "^3.0.7"
    ansi-escapes "^4.3.2"

"@inquirer/prompts@^7.6.0":
  version "7.6.0"
  dependencies:
    "@inquirer/checkbox" "^4.1.9"
    "@inquirer/confirm" "^5.1.13"
    "@inquirer/editor" "^4.2.14"
    "@inquirer/expand" "^4.0.16"
    "@inquirer/input" "^4.2.0"
    "@inquirer/number" "^3.0.16"
    "@inquirer/password" "^4.0.16"
    "@inquirer/rawlist" "^4.1.4"
    "@inquirer/search" "^3.0.16"
    "@inquirer/select" "^4.2.4"

"@inquirer/rawlist@^4.1.4":
  version "4.1.4"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/type" "^3.0.7"
    yoctocolors-cjs "^2.1.2"

"@inquirer/search@^3.0.16":
  version "3.0.16"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/figures" "^1.0.12"
    "@inquirer/type" "^3.0.7"
    yoctocolors-cjs "^2.1.2"

"@inquirer/select@^4.2.4":
  version "4.2.4"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/figures" "^1.0.12"
    "@inquirer/type" "^3.0.7"
    ansi-escapes "^4.3.2"
    yoctocolors-cjs "^2.1.2"

"@inquirer/type@^3.0.7":
  version "3.0.7"

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@isaacs/fs-minipass@^4.0.0":
  version "4.0.1"
  dependencies:
    minipass "^7.0.4"

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2", "@istanbuljs/schema@^0.1.3":
  version "0.1.3"

"@jest/console@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/types" "30.0.5"
    "@types/node" "*"
    chalk "^4.1.2"
    jest-message-util "30.0.5"
    jest-util "30.0.5"
    slash "^3.0.0"

"@jest/core@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/console" "30.0.5"
    "@jest/pattern" "30.0.1"
    "@jest/reporters" "30.0.5"
    "@jest/test-result" "30.0.5"
    "@jest/transform" "30.0.5"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    ansi-escapes "^4.3.2"
    chalk "^4.1.2"
    ci-info "^4.2.0"
    exit-x "^0.2.2"
    graceful-fs "^4.2.11"
    jest-changed-files "30.0.5"
    jest-config "30.0.5"
    jest-haste-map "30.0.5"
    jest-message-util "30.0.5"
    jest-regex-util "30.0.1"
    jest-resolve "30.0.5"
    jest-resolve-dependencies "30.0.5"
    jest-runner "30.0.5"
    jest-runtime "30.0.5"
    jest-snapshot "30.0.5"
    jest-util "30.0.5"
    jest-validate "30.0.5"
    jest-watcher "30.0.5"
    micromatch "^4.0.8"
    pretty-format "30.0.5"
    slash "^3.0.0"

"@jest/diff-sequences@30.0.1":
  version "30.0.1"

"@jest/environment-jsdom-abstract@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/environment" "30.0.5"
    "@jest/fake-timers" "30.0.5"
    "@jest/types" "30.0.5"
    "@types/jsdom" "^21.1.7"
    "@types/node" "*"
    jest-mock "30.0.5"
    jest-util "30.0.5"

"@jest/environment@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/fake-timers" "30.0.5"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    jest-mock "30.0.5"

"@jest/expect-utils@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/get-type" "30.0.1"

"@jest/expect@30.0.5":
  version "30.0.5"
  dependencies:
    expect "30.0.5"
    jest-snapshot "30.0.5"

"@jest/fake-timers@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/types" "30.0.5"
    "@sinonjs/fake-timers" "^13.0.0"
    "@types/node" "*"
    jest-message-util "30.0.5"
    jest-mock "30.0.5"
    jest-util "30.0.5"

"@jest/get-type@30.0.1":
  version "30.0.1"

"@jest/globals@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/environment" "30.0.5"
    "@jest/expect" "30.0.5"
    "@jest/types" "30.0.5"
    jest-mock "30.0.5"

"@jest/pattern@30.0.1":
  version "30.0.1"
  dependencies:
    "@types/node" "*"
    jest-regex-util "30.0.1"

"@jest/reporters@30.0.5":
  version "30.0.5"
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "30.0.5"
    "@jest/test-result" "30.0.5"
    "@jest/transform" "30.0.5"
    "@jest/types" "30.0.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    "@types/node" "*"
    chalk "^4.1.2"
    collect-v8-coverage "^1.0.2"
    exit-x "^0.2.2"
    glob "^10.3.10"
    graceful-fs "^4.2.11"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^6.0.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^5.0.0"
    istanbul-reports "^3.1.3"
    jest-message-util "30.0.5"
    jest-util "30.0.5"
    jest-worker "30.0.5"
    slash "^3.0.0"
    string-length "^4.0.2"
    v8-to-istanbul "^9.0.1"

"@jest/schemas@30.0.5":
  version "30.0.5"
  dependencies:
    "@sinclair/typebox" "^0.34.0"

"@jest/snapshot-utils@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/types" "30.0.5"
    chalk "^4.1.2"
    graceful-fs "^4.2.11"
    natural-compare "^1.4.0"

"@jest/source-map@30.0.1":
  version "30.0.1"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    callsites "^3.1.0"
    graceful-fs "^4.2.11"

"@jest/test-result@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/console" "30.0.5"
    "@jest/types" "30.0.5"
    "@types/istanbul-lib-coverage" "^2.0.6"
    collect-v8-coverage "^1.0.2"

"@jest/test-sequencer@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/test-result" "30.0.5"
    graceful-fs "^4.2.11"
    jest-haste-map "30.0.5"
    slash "^3.0.0"

"@jest/transform@^29.0.0 || ^30.0.0", "@jest/transform@30.0.5":
  version "30.0.5"
  dependencies:
    "@babel/core" "^7.27.4"
    "@jest/types" "30.0.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    babel-plugin-istanbul "^7.0.0"
    chalk "^4.1.2"
    convert-source-map "^2.0.0"
    fast-json-stable-stringify "^2.1.0"
    graceful-fs "^4.2.11"
    jest-haste-map "30.0.5"
    jest-regex-util "30.0.1"
    jest-util "30.0.5"
    micromatch "^4.0.8"
    pirates "^4.0.7"
    slash "^3.0.0"
    write-file-atomic "^5.0.1"

"@jest/types@^29.0.0 || ^30.0.0", "@jest/types@30.0.5":
  version "30.0.5"
  dependencies:
    "@jest/pattern" "30.0.1"
    "@jest/schemas" "30.0.5"
    "@types/istanbul-lib-coverage" "^2.0.6"
    "@types/istanbul-reports" "^3.0.4"
    "@types/node" "*"
    "@types/yargs" "^17.0.33"
    chalk "^4.1.2"

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.10"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"

"@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.2"

"@jridgewell/trace-mapping@^0.3.12", "@jridgewell/trace-mapping@^0.3.23", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.27"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@mswjs/interceptors@^0.39.1":
  version "0.39.4"
  dependencies:
    "@open-draft/deferred-promise" "^2.2.0"
    "@open-draft/logger" "^0.3.0"
    "@open-draft/until" "^2.0.0"
    is-node-process "^1.2.0"
    outvariant "^1.4.3"
    strict-event-emitter "^0.5.1"

"@next-auth/prisma-adapter@^1.0.7":
  version "1.0.7"

"@next/env@15.4.2":
  version "15.4.2"

"@next/eslint-plugin-next@15.2.3":
  version "15.2.3"
  dependencies:
    fast-glob "3.3.1"

"@next/swc-darwin-arm64@15.4.2":
  version "15.4.2"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@nolyfill/is-core-module@1.0.39":
  version "1.0.39"

"@open-draft/deferred-promise@^2.2.0":
  version "2.2.0"

"@open-draft/logger@^0.3.0":
  version "0.3.0"
  dependencies:
    is-node-process "^1.2.0"
    outvariant "^1.4.0"

"@open-draft/until@^2.0.0", "@open-draft/until@^2.1.0":
  version "2.1.0"

"@oxlint/darwin-arm64@1.5.0":
  version "1.5.0"

"@panva/hkdf@^1.0.2":
  version "1.2.1"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"

"@pkgr/core@^0.2.9":
  version "0.2.9"

"@playwright/test@^1.51.1", "@playwright/test@^1.54.1":
  version "1.54.1"
  dependencies:
    playwright "1.54.1"

"@prisma/client@^6.12.0", "@prisma/client@>=2.26.0 || >=3":
  version "6.12.0"

"@prisma/config@6.12.0":
  version "6.12.0"
  dependencies:
    jiti "2.4.2"

"@prisma/debug@6.12.0":
  version "6.12.0"

"@prisma/engines-version@6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc":
  version "6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc"

"@prisma/engines@6.12.0":
  version "6.12.0"
  dependencies:
    "@prisma/debug" "6.12.0"
    "@prisma/engines-version" "6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc"
    "@prisma/fetch-engine" "6.12.0"
    "@prisma/get-platform" "6.12.0"

"@prisma/fetch-engine@6.12.0":
  version "6.12.0"
  dependencies:
    "@prisma/debug" "6.12.0"
    "@prisma/engines-version" "6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc"
    "@prisma/get-platform" "6.12.0"

"@prisma/get-platform@6.12.0":
  version "6.12.0"
  dependencies:
    "@prisma/debug" "6.12.0"

"@radix-ui/number@1.1.1":
  version "1.1.1"

"@radix-ui/primitive@1.1.2":
  version "1.1.2"

"@radix-ui/react-accessible-icon@1.1.7":
  version "1.1.7"
  dependencies:
    "@radix-ui/react-visually-hidden" "1.2.3"

"@radix-ui/react-accordion@1.2.11":
  version "1.2.11"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collapsible" "1.1.11"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-alert-dialog@^1.1.14", "@radix-ui/react-alert-dialog@1.1.14":
  version "1.1.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dialog" "1.1.14"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-arrow@1.1.7":
  version "1.1.7"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-aspect-ratio@1.1.7":
  version "1.1.7"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-avatar@^1.1.2", "@radix-ui/react-avatar@1.1.10":
  version "1.1.10"
  dependencies:
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-is-hydrated" "0.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-checkbox@^1.3.2", "@radix-ui/react-checkbox@1.3.2":
  version "1.3.2"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-collapsible@1.1.11":
  version "1.1.11"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-collection@1.1.7":
  version "1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-compose-refs@^1.1.1", "@radix-ui/react-compose-refs@1.1.2":
  version "1.1.2"

"@radix-ui/react-context-menu@2.2.15":
  version "2.2.15"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-menu" "2.1.15"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-context@1.1.2":
  version "1.1.2"

"@radix-ui/react-dialog@^1.1.14", "@radix-ui/react-dialog@^1.1.6", "@radix-ui/react-dialog@1.1.14":
  version "1.1.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-direction@1.1.1":
  version "1.1.1"

"@radix-ui/react-dismissable-layer@1.1.10":
  version "1.1.10"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-escape-keydown" "1.1.1"

"@radix-ui/react-dropdown-menu@^2.1.15", "@radix-ui/react-dropdown-menu@2.1.15":
  version "2.1.15"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.15"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-focus-guards@1.1.2":
  version "1.1.2"

"@radix-ui/react-focus-scope@1.1.7":
  version "1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-form@0.1.7":
  version "0.1.7"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-label" "2.1.7"
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-hover-card@1.1.14":
  version "1.1.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-id@^1.1.0", "@radix-ui/react-id@1.1.1":
  version "1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-label@^2.1.2", "@radix-ui/react-label@2.1.7":
  version "2.1.7"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-menu@2.1.15":
  version "2.1.15"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-menubar@1.1.15":
  version "1.1.15"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.15"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-navigation-menu@1.2.13":
  version "1.2.13"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.3"

"@radix-ui/react-one-time-password-field@0.1.7":
  version "0.1.7"
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-effect-event" "0.0.2"
    "@radix-ui/react-use-is-hydrated" "0.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-password-toggle-field@0.1.2":
  version "0.1.2"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-effect-event" "0.0.2"
    "@radix-ui/react-use-is-hydrated" "0.1.0"

"@radix-ui/react-popover@1.1.14":
  version "1.1.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-popper@1.2.7":
  version "1.2.7"
  dependencies:
    "@floating-ui/react-dom" "^2.0.0"
    "@radix-ui/react-arrow" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-rect" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-portal@1.1.9":
  version "1.1.9"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-presence@1.1.4":
  version "1.1.4"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-primitive@^2.0.2", "@radix-ui/react-primitive@2.1.3":
  version "2.1.3"
  dependencies:
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-progress@^1.1.7", "@radix-ui/react-progress@1.1.7":
  version "1.1.7"
  dependencies:
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-radio-group@^1.3.7", "@radix-ui/react-radio-group@1.3.7":
  version "1.3.7"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-roving-focus@1.1.10":
  version "1.1.10"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-scroll-area@^1.2.9", "@radix-ui/react-scroll-area@1.2.9":
  version "1.2.9"
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-select@^2.1.6", "@radix-ui/react-select@2.2.5":
  version "2.2.5"
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.3"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-separator@^1.1.2", "@radix-ui/react-separator@1.1.7":
  version "1.1.7"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-slider@1.3.5":
  version "1.3.5"
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-slot@^1.1.2", "@radix-ui/react-slot@1.2.3":
  version "1.2.3"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"

"@radix-ui/react-switch@^1.2.5", "@radix-ui/react-switch@1.2.5":
  version "1.2.5"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-tabs@^1.1.2", "@radix-ui/react-tabs@1.1.12":
  version "1.1.12"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-toast@1.2.14":
  version "1.2.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.3"

"@radix-ui/react-toggle-group@1.1.10":
  version "1.1.10"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-toggle" "1.1.9"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-toggle@1.1.9":
  version "1.1.9"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-toolbar@1.1.10":
  version "1.1.10"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-separator" "1.1.7"
    "@radix-ui/react-toggle-group" "1.1.10"

"@radix-ui/react-tooltip@^1.2.7", "@radix-ui/react-tooltip@1.2.7":
  version "1.2.7"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-visually-hidden" "1.2.3"

"@radix-ui/react-use-callback-ref@1.1.1":
  version "1.1.1"

"@radix-ui/react-use-controllable-state@1.2.2":
  version "1.2.2"
  dependencies:
    "@radix-ui/react-use-effect-event" "0.0.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-effect-event@0.0.2":
  version "0.0.2"
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-escape-keydown@1.1.1":
  version "1.1.1"
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-use-is-hydrated@0.1.0":
  version "0.1.0"
  dependencies:
    use-sync-external-store "^1.5.0"

"@radix-ui/react-use-layout-effect@1.1.1":
  version "1.1.1"

"@radix-ui/react-use-previous@1.1.1":
  version "1.1.1"

"@radix-ui/react-use-rect@1.1.1":
  version "1.1.1"
  dependencies:
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-use-size@1.1.1":
  version "1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-visually-hidden@1.2.3":
  version "1.2.3"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/rect@1.1.1":
  version "1.1.1"

"@remirror/core-constants@3.0.0":
  version "3.0.0"

"@rtsao/scc@^1.1.0":
  version "1.1.0"

"@rushstack/eslint-patch@^1.10.3":
  version "1.12.0"

"@sendgrid/client@^8.1.5":
  version "8.1.5"
  dependencies:
    "@sendgrid/helpers" "^8.0.0"
    axios "^1.8.2"

"@sendgrid/helpers@^8.0.0":
  version "8.0.0"
  dependencies:
    deepmerge "^4.2.2"

"@sendgrid/mail@^8.1.5":
  version "8.1.5"
  dependencies:
    "@sendgrid/client" "^8.1.5"
    "@sendgrid/helpers" "^8.0.0"

"@sinclair/typebox@^0.34.0":
  version "0.34.37"

"@sinonjs/commons@^3.0.1":
  version "3.0.1"
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^13.0.0":
  version "13.0.5"
  dependencies:
    "@sinonjs/commons" "^3.0.1"

"@smithy/abort-controller@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/config-resolver@^4.1.4":
  version "4.1.4"
  dependencies:
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/types" "^4.3.1"
    "@smithy/util-config-provider" "^4.0.0"
    "@smithy/util-middleware" "^4.0.4"
    tslib "^2.6.2"

"@smithy/core@^3.7.0", "@smithy/core@^3.7.1":
  version "3.7.1"
  dependencies:
    "@smithy/middleware-serde" "^4.0.8"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/types" "^4.3.1"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-middleware" "^4.0.4"
    "@smithy/util-stream" "^4.2.3"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/credential-provider-imds@^4.0.6":
  version "4.0.6"
  dependencies:
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/types" "^4.3.1"
    "@smithy/url-parser" "^4.0.4"
    tslib "^2.6.2"

"@smithy/fetch-http-handler@^5.1.0":
  version "5.1.0"
  dependencies:
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/querystring-builder" "^4.0.4"
    "@smithy/types" "^4.3.1"
    "@smithy/util-base64" "^4.0.0"
    tslib "^2.6.2"

"@smithy/hash-node@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/types" "^4.3.1"
    "@smithy/util-buffer-from" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/invalid-dependency@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/is-array-buffer@^2.2.0":
  version "2.2.0"
  dependencies:
    tslib "^2.6.2"

"@smithy/is-array-buffer@^4.0.0":
  version "4.0.0"
  dependencies:
    tslib "^2.6.2"

"@smithy/middleware-content-length@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/middleware-endpoint@^4.1.15", "@smithy/middleware-endpoint@^4.1.16":
  version "4.1.16"
  dependencies:
    "@smithy/core" "^3.7.1"
    "@smithy/middleware-serde" "^4.0.8"
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/shared-ini-file-loader" "^4.0.4"
    "@smithy/types" "^4.3.1"
    "@smithy/url-parser" "^4.0.4"
    "@smithy/util-middleware" "^4.0.4"
    tslib "^2.6.2"

"@smithy/middleware-retry@^4.1.16":
  version "4.1.17"
  dependencies:
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/service-error-classification" "^4.0.6"
    "@smithy/smithy-client" "^4.4.8"
    "@smithy/types" "^4.3.1"
    "@smithy/util-middleware" "^4.0.4"
    "@smithy/util-retry" "^4.0.6"
    tslib "^2.6.2"
    uuid "^9.0.1"

"@smithy/middleware-serde@^4.0.8":
  version "4.0.8"
  dependencies:
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/middleware-stack@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/node-config-provider@^4.1.3":
  version "4.1.3"
  dependencies:
    "@smithy/property-provider" "^4.0.4"
    "@smithy/shared-ini-file-loader" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/node-http-handler@^4.1.0":
  version "4.1.0"
  dependencies:
    "@smithy/abort-controller" "^4.0.4"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/querystring-builder" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/property-provider@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/protocol-http@^5.1.2":
  version "5.1.2"
  dependencies:
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/querystring-builder@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/types" "^4.3.1"
    "@smithy/util-uri-escape" "^4.0.0"
    tslib "^2.6.2"

"@smithy/querystring-parser@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/service-error-classification@^4.0.6":
  version "4.0.6"
  dependencies:
    "@smithy/types" "^4.3.1"

"@smithy/shared-ini-file-loader@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/signature-v4@^5.1.2":
  version "5.1.2"
  dependencies:
    "@smithy/is-array-buffer" "^4.0.0"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/types" "^4.3.1"
    "@smithy/util-hex-encoding" "^4.0.0"
    "@smithy/util-middleware" "^4.0.4"
    "@smithy/util-uri-escape" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/smithy-client@^4.4.7", "@smithy/smithy-client@^4.4.8":
  version "4.4.8"
  dependencies:
    "@smithy/core" "^3.7.1"
    "@smithy/middleware-endpoint" "^4.1.16"
    "@smithy/middleware-stack" "^4.0.4"
    "@smithy/protocol-http" "^5.1.2"
    "@smithy/types" "^4.3.1"
    "@smithy/util-stream" "^4.2.3"
    tslib "^2.6.2"

"@smithy/types@^4.3.1":
  version "4.3.1"
  dependencies:
    tslib "^2.6.2"

"@smithy/url-parser@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/querystring-parser" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/util-base64@^4.0.0":
  version "4.0.0"
  dependencies:
    "@smithy/util-buffer-from" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-body-length-browser@^4.0.0":
  version "4.0.0"
  dependencies:
    tslib "^2.6.2"

"@smithy/util-body-length-node@^4.0.0":
  version "4.0.0"
  dependencies:
    tslib "^2.6.2"

"@smithy/util-buffer-from@^2.2.0":
  version "2.2.0"
  dependencies:
    "@smithy/is-array-buffer" "^2.2.0"
    tslib "^2.6.2"

"@smithy/util-buffer-from@^4.0.0":
  version "4.0.0"
  dependencies:
    "@smithy/is-array-buffer" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-config-provider@^4.0.0":
  version "4.0.0"
  dependencies:
    tslib "^2.6.2"

"@smithy/util-defaults-mode-browser@^4.0.23":
  version "4.0.24"
  dependencies:
    "@smithy/property-provider" "^4.0.4"
    "@smithy/smithy-client" "^4.4.8"
    "@smithy/types" "^4.3.1"
    bowser "^2.11.0"
    tslib "^2.6.2"

"@smithy/util-defaults-mode-node@^4.0.23":
  version "4.0.24"
  dependencies:
    "@smithy/config-resolver" "^4.1.4"
    "@smithy/credential-provider-imds" "^4.0.6"
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/property-provider" "^4.0.4"
    "@smithy/smithy-client" "^4.4.8"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/util-endpoints@^3.0.6":
  version "3.0.6"
  dependencies:
    "@smithy/node-config-provider" "^4.1.3"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/util-hex-encoding@^4.0.0":
  version "4.0.0"
  dependencies:
    tslib "^2.6.2"

"@smithy/util-middleware@^4.0.4":
  version "4.0.4"
  dependencies:
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/util-retry@^4.0.6":
  version "4.0.6"
  dependencies:
    "@smithy/service-error-classification" "^4.0.6"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@smithy/util-stream@^4.2.3":
  version "4.2.3"
  dependencies:
    "@smithy/fetch-http-handler" "^5.1.0"
    "@smithy/node-http-handler" "^4.1.0"
    "@smithy/types" "^4.3.1"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-buffer-from" "^4.0.0"
    "@smithy/util-hex-encoding" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-uri-escape@^4.0.0":
  version "4.0.0"
  dependencies:
    tslib "^2.6.2"

"@smithy/util-utf8@^2.0.0":
  version "2.3.0"
  dependencies:
    "@smithy/util-buffer-from" "^2.2.0"
    tslib "^2.6.2"

"@smithy/util-utf8@^4.0.0":
  version "4.0.0"
  dependencies:
    "@smithy/util-buffer-from" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-waiter@^4.0.6":
  version "4.0.6"
  dependencies:
    "@smithy/abort-controller" "^4.0.4"
    "@smithy/types" "^4.3.1"
    tslib "^2.6.2"

"@standard-schema/utils@^0.3.0":
  version "0.3.0"

"@swc/helpers@0.5.15":
  version "0.5.15"
  dependencies:
    tslib "^2.8.0"

"@tailwindcss/node@4.1.11":
  version "4.1.11"
  dependencies:
    "@ampproject/remapping" "^2.3.0"
    enhanced-resolve "^5.18.1"
    jiti "^2.4.2"
    lightningcss "1.30.1"
    magic-string "^0.30.17"
    source-map-js "^1.2.1"
    tailwindcss "4.1.11"

"@tailwindcss/oxide-darwin-arm64@4.1.11":
  version "4.1.11"

"@tailwindcss/oxide@4.1.11":
  version "4.1.11"
  dependencies:
    detect-libc "^2.0.4"
    tar "^7.4.3"
  optionalDependencies:
    "@tailwindcss/oxide-android-arm64" "4.1.11"
    "@tailwindcss/oxide-darwin-arm64" "4.1.11"
    "@tailwindcss/oxide-darwin-x64" "4.1.11"
    "@tailwindcss/oxide-freebsd-x64" "4.1.11"
    "@tailwindcss/oxide-linux-arm-gnueabihf" "4.1.11"
    "@tailwindcss/oxide-linux-arm64-gnu" "4.1.11"
    "@tailwindcss/oxide-linux-arm64-musl" "4.1.11"
    "@tailwindcss/oxide-linux-x64-gnu" "4.1.11"
    "@tailwindcss/oxide-linux-x64-musl" "4.1.11"
    "@tailwindcss/oxide-wasm32-wasi" "4.1.11"
    "@tailwindcss/oxide-win32-arm64-msvc" "4.1.11"
    "@tailwindcss/oxide-win32-x64-msvc" "4.1.11"

"@tailwindcss/postcss@^4.1.10":
  version "4.1.11"
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    "@tailwindcss/node" "4.1.11"
    "@tailwindcss/oxide" "4.1.11"
    postcss "^8.4.41"
    tailwindcss "4.1.11"

"@tanstack/query-core@5.83.0":
  version "5.83.0"

"@tanstack/react-query@^5.83.0":
  version "5.83.0"
  dependencies:
    "@tanstack/query-core" "5.83.0"

"@testing-library/dom@^10.0.0", "@testing-library/dom@^10.4.0", "@testing-library/dom@>=7.21.4":
  version "10.4.0"
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/runtime" "^7.12.5"
    "@types/aria-query" "^5.0.1"
    aria-query "5.3.0"
    chalk "^4.1.0"
    dom-accessibility-api "^0.5.9"
    lz-string "^1.5.0"
    pretty-format "^27.0.2"

"@testing-library/jest-dom@^6.6.3":
  version "6.6.3"
  dependencies:
    "@adobe/css-tools" "^4.4.0"
    aria-query "^5.0.0"
    chalk "^3.0.0"
    css.escape "^1.5.1"
    dom-accessibility-api "^0.6.3"
    lodash "^4.17.21"
    redent "^3.0.0"

"@testing-library/react@^16.3.0":
  version "16.3.0"
  dependencies:
    "@babel/runtime" "^7.12.5"

"@testing-library/user-event@^14.6.1":
  version "14.6.1"

"@tiptap/core@^3.0.7":
  version "3.0.7"

"@tiptap/extension-blockquote@^3.0.7":
  version "3.0.7"

"@tiptap/extension-bold@^3.0.7":
  version "3.0.7"

"@tiptap/extension-bubble-menu@^3.0.7":
  version "3.0.7"
  dependencies:
    "@floating-ui/dom" "^1.0.0"

"@tiptap/extension-bullet-list@^3.0.7":
  version "3.0.7"

"@tiptap/extension-code-block@^3.0.7":
  version "3.0.7"

"@tiptap/extension-code@^3.0.7":
  version "3.0.7"

"@tiptap/extension-document@^3.0.7":
  version "3.0.7"

"@tiptap/extension-dropcursor@^3.0.7":
  version "3.0.7"

"@tiptap/extension-floating-menu@^3.0.7":
  version "3.0.7"

"@tiptap/extension-gapcursor@^3.0.7":
  version "3.0.7"

"@tiptap/extension-hard-break@^3.0.7":
  version "3.0.7"

"@tiptap/extension-heading@^3.0.7":
  version "3.0.7"

"@tiptap/extension-horizontal-rule@^3.0.7":
  version "3.0.7"

"@tiptap/extension-image@^3.0.7":
  version "3.0.7"

"@tiptap/extension-italic@^3.0.7":
  version "3.0.7"

"@tiptap/extension-link@^3.0.7":
  version "3.0.7"
  dependencies:
    linkifyjs "^4.2.0"

"@tiptap/extension-list-item@^3.0.7":
  version "3.0.7"

"@tiptap/extension-list-keymap@^3.0.7":
  version "3.0.7"

"@tiptap/extension-list@^3.0.7":
  version "3.0.7"

"@tiptap/extension-ordered-list@^3.0.7":
  version "3.0.7"

"@tiptap/extension-paragraph@^3.0.7":
  version "3.0.7"

"@tiptap/extension-strike@^3.0.7":
  version "3.0.7"

"@tiptap/extension-text-align@^3.0.7":
  version "3.0.7"

"@tiptap/extension-text@^3.0.7":
  version "3.0.7"

"@tiptap/extension-underline@^3.0.7":
  version "3.0.7"

"@tiptap/extensions@^3.0.7":
  version "3.0.7"

"@tiptap/pm@^3.0.7":
  version "3.0.7"
  dependencies:
    prosemirror-changeset "^2.3.0"
    prosemirror-collab "^1.3.1"
    prosemirror-commands "^1.6.2"
    prosemirror-dropcursor "^1.8.1"
    prosemirror-gapcursor "^1.3.2"
    prosemirror-history "^1.4.1"
    prosemirror-inputrules "^1.4.0"
    prosemirror-keymap "^1.2.2"
    prosemirror-markdown "^1.13.1"
    prosemirror-menu "^1.2.4"
    prosemirror-model "^1.24.1"
    prosemirror-schema-basic "^1.2.3"
    prosemirror-schema-list "^1.5.0"
    prosemirror-state "^1.4.3"
    prosemirror-tables "^1.6.4"
    prosemirror-trailing-node "^3.0.0"
    prosemirror-transform "^1.10.2"
    prosemirror-view "^1.38.1"

"@tiptap/react@^3.0.7":
  version "3.0.7"
  dependencies:
    "@types/use-sync-external-store" "^0.0.6"
    fast-deep-equal "^3.1.3"
    use-sync-external-store "^1.4.0"
  optionalDependencies:
    "@tiptap/extension-bubble-menu" "^3.0.7"
    "@tiptap/extension-floating-menu" "^3.0.7"

"@tiptap/starter-kit@^3.0.7":
  version "3.0.7"
  dependencies:
    "@tiptap/core" "^3.0.7"
    "@tiptap/extension-blockquote" "^3.0.7"
    "@tiptap/extension-bold" "^3.0.7"
    "@tiptap/extension-bullet-list" "^3.0.7"
    "@tiptap/extension-code" "^3.0.7"
    "@tiptap/extension-code-block" "^3.0.7"
    "@tiptap/extension-document" "^3.0.7"
    "@tiptap/extension-dropcursor" "^3.0.7"
    "@tiptap/extension-gapcursor" "^3.0.7"
    "@tiptap/extension-hard-break" "^3.0.7"
    "@tiptap/extension-heading" "^3.0.7"
    "@tiptap/extension-horizontal-rule" "^3.0.7"
    "@tiptap/extension-italic" "^3.0.7"
    "@tiptap/extension-link" "^3.0.7"
    "@tiptap/extension-list" "^3.0.7"
    "@tiptap/extension-list-item" "^3.0.7"
    "@tiptap/extension-list-keymap" "^3.0.7"
    "@tiptap/extension-ordered-list" "^3.0.7"
    "@tiptap/extension-paragraph" "^3.0.7"
    "@tiptap/extension-strike" "^3.0.7"
    "@tiptap/extension-text" "^3.0.7"
    "@tiptap/extension-underline" "^3.0.7"
    "@tiptap/extensions" "^3.0.7"
    "@tiptap/pm" "^3.0.7"

"@types/aria-query@^5.0.1":
  version "5.0.4"

"@types/babel__core@^7.20.5":
  version "7.20.5"
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.27.0"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*":
  version "7.20.7"
  dependencies:
    "@babel/types" "^7.20.7"

"@types/bcryptjs@^2.4.6":
  version "2.4.6"

"@types/cookie@^0.6.0":
  version "0.6.0"

"@types/d3-array@^3.0.3":
  version "3.2.1"

"@types/d3-color@*":
  version "3.1.3"

"@types/d3-ease@^3.0.0":
  version "3.0.2"

"@types/d3-interpolate@^3.0.1":
  version "3.0.4"
  dependencies:
    "@types/d3-color" "*"

"@types/d3-path@*":
  version "3.1.1"

"@types/d3-scale@^4.0.2":
  version "4.0.9"
  dependencies:
    "@types/d3-time" "*"

"@types/d3-shape@^3.1.0":
  version "3.1.7"
  dependencies:
    "@types/d3-path" "*"

"@types/d3-time@*", "@types/d3-time@^3.0.0":
  version "3.0.4"

"@types/d3-timer@^3.0.0":
  version "3.0.2"

"@types/estree@^1.0.6":
  version "1.0.8"

"@types/inquirer@^9.0.8":
  version "9.0.8"
  dependencies:
    "@types/through" "*"
    rxjs "^7.2.0"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.1", "@types/istanbul-lib-coverage@^2.0.6":
  version "2.0.6"

"@types/istanbul-lib-report@*":
  version "3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.4":
  version "3.0.4"
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@^30.0.0":
  version "30.0.0"
  dependencies:
    expect "^30.0.0"
    pretty-format "^30.0.0"

"@types/jsdom@^21.1.7":
  version "21.1.7"
  dependencies:
    "@types/node" "*"
    "@types/tough-cookie" "*"
    parse5 "^7.0.0"

"@types/json-schema@^7.0.15":
  version "7.0.15"

"@types/json5@^0.0.29":
  version "0.0.29"

"@types/jsonwebtoken@^9.0.9":
  version "9.0.10"
  dependencies:
    "@types/ms" "*"
    "@types/node" "*"

"@types/linkify-it@^5":
  version "5.0.0"

"@types/lodash@^4.17.20":
  version "4.17.20"

"@types/markdown-it@^14.0.0":
  version "14.1.2"
  dependencies:
    "@types/linkify-it" "^5"
    "@types/mdurl" "^2"

"@types/mdurl@^2":
  version "2.0.0"

"@types/ms@*":
  version "2.1.0"

"@types/node@*", "@types/node@^20", "@types/node@>=18":
  version "20.19.2"
  dependencies:
    undici-types "~6.21.0"

"@types/nodemailer@^6.4.17":
  version "6.4.17"
  dependencies:
    "@types/node" "*"

"@types/nprogress@^0.2.3":
  version "0.2.3"

"@types/react-dom@*", "@types/react-dom@^18.0.0 || ^19.0.0", "@types/react-dom@^19":
  version "19.1.6"

"@types/react@*", "@types/react@^18.0.0 || ^19.0.0", "@types/react@^19", "@types/react@^19.0.0", "@types/react@>=18.0.0":
  version "19.1.8"
  dependencies:
    csstype "^3.0.2"

"@types/sendgrid@^4.3.0":
  version "4.3.0"
  dependencies:
    sendgrid "*"

"@types/stack-utils@^2.0.3":
  version "2.0.3"

"@types/statuses@^2.0.4":
  version "2.0.6"

"@types/through@*":
  version "0.0.33"
  dependencies:
    "@types/node" "*"

"@types/tough-cookie@*", "@types/tough-cookie@^4.0.5":
  version "4.0.5"

"@types/use-sync-external-store@^0.0.6":
  version "0.0.6"

"@types/ws@^8.18.1":
  version "8.18.1"
  resolved "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz"
  integrity sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==
  dependencies:
    "@types/node" "*"

"@types/xlsx@^0.0.35":
  version "0.0.35"

"@types/yargs-parser@*":
  version "21.0.3"

"@types/yargs@^17.0.33":
  version "17.0.33"
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0", "@typescript-eslint/eslint-plugin@8.35.1":
  version "8.35.1"
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.35.1"
    "@typescript-eslint/type-utils" "8.35.1"
    "@typescript-eslint/utils" "8.35.1"
    "@typescript-eslint/visitor-keys" "8.35.1"
    graphemer "^1.4.0"
    ignore "^7.0.0"
    natural-compare "^1.4.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/parser@^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0", "@typescript-eslint/parser@^8.35.1", "@typescript-eslint/parser@8.35.1":
  version "8.35.1"
  dependencies:
    "@typescript-eslint/scope-manager" "8.35.1"
    "@typescript-eslint/types" "8.35.1"
    "@typescript-eslint/typescript-estree" "8.35.1"
    "@typescript-eslint/visitor-keys" "8.35.1"
    debug "^4.3.4"

"@typescript-eslint/project-service@8.35.1":
  version "8.35.1"
  dependencies:
    "@typescript-eslint/tsconfig-utils" "^8.35.1"
    "@typescript-eslint/types" "^8.35.1"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@8.35.1":
  version "8.35.1"
  dependencies:
    "@typescript-eslint/types" "8.35.1"
    "@typescript-eslint/visitor-keys" "8.35.1"

"@typescript-eslint/tsconfig-utils@^8.35.1", "@typescript-eslint/tsconfig-utils@8.35.1":
  version "8.35.1"

"@typescript-eslint/type-utils@8.35.1":
  version "8.35.1"
  dependencies:
    "@typescript-eslint/typescript-estree" "8.35.1"
    "@typescript-eslint/utils" "8.35.1"
    debug "^4.3.4"
    ts-api-utils "^2.1.0"

"@typescript-eslint/types@^8.35.1", "@typescript-eslint/types@8.35.1":
  version "8.35.1"

"@typescript-eslint/typescript-estree@8.35.1":
  version "8.35.1"
  dependencies:
    "@typescript-eslint/project-service" "8.35.1"
    "@typescript-eslint/tsconfig-utils" "8.35.1"
    "@typescript-eslint/types" "8.35.1"
    "@typescript-eslint/visitor-keys" "8.35.1"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/utils@8.35.1":
  version "8.35.1"
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.35.1"
    "@typescript-eslint/types" "8.35.1"
    "@typescript-eslint/typescript-estree" "8.35.1"

"@typescript-eslint/visitor-keys@8.35.1":
  version "8.35.1"
  dependencies:
    "@typescript-eslint/types" "8.35.1"
    eslint-visitor-keys "^4.2.1"

"@ungap/structured-clone@^1.3.0":
  version "1.3.0"

"@unrs/resolver-binding-darwin-arm64@1.9.2":
  version "1.9.2"

"@zxing/text-encoding@0.9.0":
  version "0.9.0"

acorn-jsx@^5.3.2:
  version "5.3.2"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8.15.0:
  version "8.15.0"

addressparser@~1.0.1:
  version "1.0.1"

adler-32@~1.3.0:
  version "1.3.1"

agent-base@^7.1.0, agent-base@^7.1.2:
  version "7.1.4"

ajv@^6.12.4:
  version "6.12.6"
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-escapes@^4.3.2:
  version "4.3.2"
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"

ansi-regex@^6.0.1:
  version "6.1.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"

ansi-styles@^5.2.0:
  version "5.2.0"

ansi-styles@^6.1.0:
  version "6.2.1"

anymatch@^3.1.3:
  version "3.1.3"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^1.0.7:
  version "1.0.10"
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"

aria-hidden@^1.2.4:
  version "1.2.6"
  dependencies:
    tslib "^2.0.0"

aria-query@^5.0.0, aria-query@^5.3.2:
  version "5.3.2"

aria-query@5.3.0:
  version "5.3.0"
  dependencies:
    dequal "^2.0.3"

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-includes@^3.1.6, array-includes@^3.1.8, array-includes@^3.1.9:
  version "3.1.9"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.24.0"
    es-object-atoms "^1.1.1"
    get-intrinsic "^1.3.0"
    is-string "^1.1.1"
    math-intrinsics "^1.1.0"

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.findlastindex@^1.2.6:
  version "1.2.6"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    es-shim-unscopables "^1.1.0"

array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.3:
  version "1.3.3"
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.flatmap@^1.3.2, array.prototype.flatmap@^1.3.3:
  version "1.3.3"
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

ast-types-flow@^0.0.8:
  version "0.0.8"

async-function@^1.0.0:
  version "1.0.0"

async.ensureasync@^0.5.2:
  version "0.5.2"
  dependencies:
    async.util.ensureasync "0.5.2"

async.queue@^0.5.2:
  version "0.5.2"
  dependencies:
    async.util.queue "0.5.2"

async.util.arrayeach@0.5.2:
  version "0.5.2"

async.util.ensureasync@0.5.2:
  version "0.5.2"
  dependencies:
    async.util.restparam "0.5.2"
    async.util.setimmediate "0.5.2"

async.util.isarray@0.5.2:
  version "0.5.2"

async.util.map@0.5.2:
  version "0.5.2"

async.util.noop@0.5.2:
  version "0.5.2"

async.util.onlyonce@0.5.2:
  version "0.5.2"

async.util.queue@0.5.2:
  version "0.5.2"
  dependencies:
    async.util.arrayeach "0.5.2"
    async.util.isarray "0.5.2"
    async.util.map "0.5.2"
    async.util.noop "0.5.2"
    async.util.onlyonce "0.5.2"
    async.util.setimmediate "0.5.2"

async.util.restparam@0.5.2:
  version "0.5.2"

async.util.setimmediate@0.5.2:
  version "0.5.2"

async@^3.2.3, async@^3.2.4:
  version "3.2.6"

asynckit@^0.4.0:
  version "0.4.0"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  dependencies:
    possible-typed-array-names "^1.0.0"

axe-core@^4.10.0:
  version "4.10.3"

axios@^1.8.2:
  version "1.10.0"
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axobject-query@^4.1.0:
  version "4.1.0"

"babel-jest@^29.0.0 || ^30.0.0", babel-jest@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/transform" "30.0.5"
    "@types/babel__core" "^7.20.5"
    babel-plugin-istanbul "^7.0.0"
    babel-preset-jest "30.0.1"
    chalk "^4.1.2"
    graceful-fs "^4.2.11"
    slash "^3.0.0"

babel-plugin-istanbul@^7.0.0:
  version "7.0.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.3"
    istanbul-lib-instrument "^6.0.2"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@30.0.1:
  version "30.0.1"
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.3"
    "@types/babel__core" "^7.20.5"

babel-preset-current-node-syntax@^1.1.0:
  version "1.1.0"
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-import-attributes" "^7.24.7"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"

babel-preset-jest@30.0.1:
  version "30.0.1"
  dependencies:
    babel-plugin-jest-hoist "30.0.1"
    babel-preset-current-node-syntax "^1.1.0"

balanced-match@^1.0.0:
  version "1.0.2"

bcryptjs@^3.0.2:
  version "3.0.2"

block-stream2@^2.1.0:
  version "2.1.0"
  dependencies:
    readable-stream "^3.4.0"

bottleneck@^1.12.0:
  version "1.16.0"

bowser@^2.11.0:
  version "2.11.0"

brace-expansion@^1.1.7:
  version "1.1.12"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.2"
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3:
  version "3.0.3"
  dependencies:
    fill-range "^7.1.1"

browser-or-node@^2.1.1:
  version "2.1.1"

browserslist@^4.24.0, "browserslist@>= 4.21.0":
  version "4.25.1"
  dependencies:
    caniuse-lite "^1.0.30001726"
    electron-to-chromium "^1.5.173"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

bs-logger@^0.2.6:
  version "0.2.6"
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  dependencies:
    node-int64 "^0.4.0"

buffer-crc32@^1.0.0:
  version "1.0.0"

buffer-equal-constant-time@^1.0.1:
  version "1.0.1"

buffer-from@^1.0.0:
  version "1.1.2"

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.7, call-bind@^1.0.8:
  version "1.0.8"
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0, callsites@^3.1.0:
  version "3.1.0"

camelcase@^5.3.1:
  version "5.3.1"

camelcase@^6.3.0:
  version "6.3.0"

caniuse-lite@^1.0.30001579, caniuse-lite@^1.0.30001726:
  version "1.0.30001726"

cfb@~1.2.1:
  version "1.2.2"
  dependencies:
    adler-32 "~1.3.0"
    crc-32 "~1.2.0"

chalk@^3.0.0:
  version "3.0.0"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0:
  version "4.1.2"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.2:
  version "4.1.2"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.1.0:
  version "4.1.2"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.1.2:
  version "4.1.2"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^5.4.1:
  version "5.4.1"

char-regex@^1.0.2:
  version "1.0.2"

chardet@^0.7.0:
  version "0.7.0"

chownr@^3.0.0:
  version "3.0.0"

ci-info@^4.2.0:
  version "4.2.0"

cjs-module-lexer@^2.1.0:
  version "2.1.0"

class-variance-authority@^0.7.1:
  version "0.7.1"
  dependencies:
    clsx "^2.1.1"

cli-width@^4.1.0:
  version "4.1.0"

client-only@0.0.1:
  version "0.0.1"

cliui@^8.0.1:
  version "8.0.1"
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone@2.x:
  version "2.1.2"

clsx@^2.0.0, clsx@^2.1.1:
  version "2.1.1"

cmdk@^1.1.1:
  version "1.1.1"
  dependencies:
    "@radix-ui/react-compose-refs" "^1.1.1"
    "@radix-ui/react-dialog" "^1.1.6"
    "@radix-ui/react-id" "^1.1.0"
    "@radix-ui/react-primitive" "^2.0.2"

co@^4.6.0:
  version "4.6.0"

codepage@~1.15.0:
  version "1.15.0"

collect-v8-coverage@^1.0.2:
  version "1.0.2"

color-convert@^2.0.1:
  version "2.0.1"
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"

color-string@^1.9.0:
  version "1.9.1"
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^4.2.3:
  version "4.2.3"
  dependencies:
    color-convert "^2.0.1"
    color-string "^1.9.0"

combined-stream@^1.0.8:
  version "1.0.8"
  dependencies:
    delayed-stream "~1.0.0"

commander@^14.0.0:
  version "14.0.0"

concat-map@0.0.1:
  version "0.0.1"

convert-source-map@^2.0.0:
  version "2.0.0"

cookie@^0.7.0, cookie@^0.7.2:
  version "0.7.2"

core-js@^3.5.0:
  version "3.44.0"

crc-32@~1.2.0, crc-32@~1.2.1:
  version "1.2.2"

crelt@^1.0.0:
  version "1.0.6"

cross-spawn@^7.0.3, cross-spawn@^7.0.6:
  version "7.0.6"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css.escape@^1.5.1:
  version "1.5.1"

cssstyle@^4.2.1:
  version "4.6.0"
  dependencies:
    "@asamuzakjp/css-color" "^3.2.0"
    rrweb-cssom "^0.8.0"

csstype@^3.0.2:
  version "3.1.3"

d3-array@^3.1.6, "d3-array@2 - 3", "d3-array@2.10.0 - 3":
  version "3.2.4"
  dependencies:
    internmap "1 - 2"

"d3-color@1 - 3":
  version "3.1.0"

d3-ease@^3.0.1:
  version "3.0.1"

"d3-format@1 - 3":
  version "3.1.0"

d3-interpolate@^3.0.1, "d3-interpolate@1.2.0 - 3":
  version "3.0.1"
  dependencies:
    d3-color "1 - 3"

d3-path@^3.1.0:
  version "3.1.0"

d3-scale@^4.0.2:
  version "4.0.2"
  dependencies:
    d3-array "2.10.0 - 3"
    d3-format "1 - 3"
    d3-interpolate "1.2.0 - 3"
    d3-time "2.1.1 - 3"
    d3-time-format "2 - 4"

d3-shape@^3.1.0:
  version "3.2.0"
  dependencies:
    d3-path "^3.1.0"

"d3-time-format@2 - 4":
  version "4.1.0"
  dependencies:
    d3-time "1 - 3"

d3-time@^3.0.0, "d3-time@1 - 3", "d3-time@2.1.1 - 3":
  version "3.1.0"
  dependencies:
    d3-array "2 - 3"

d3-timer@^3.0.1:
  version "3.0.1"

damerau-levenshtein@^1.0.8:
  version "1.0.8"

data-uri-to-buffer@^4.0.0:
  version "4.0.1"

data-urls@^5.0.0:
  version "5.0.0"
  dependencies:
    whatwg-mimetype "^4.0.0"
    whatwg-url "^14.0.0"

data-view-buffer@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

date-fns@^4.1.0:
  version "4.1.0"

debug@^2.2.0:
  version "2.6.9"
  dependencies:
    ms "2.0.0"

debug@^3.2.7:
  version "3.2.7"
  dependencies:
    ms "^2.1.1"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4, debug@^4.4.0, debug@4:
  version "4.4.1"
  dependencies:
    ms "^2.1.3"

decimal.js-light@^2.4.1:
  version "2.5.1"

decimal.js@^10.5.0:
  version "10.6.0"

decode-uri-component@^0.2.2:
  version "0.2.2"

dedent@^1.6.0:
  version "1.6.0"

deep-is@^0.1.3:
  version "0.1.4"

deepmerge@^4.2.2, deepmerge@^4.3.1:
  version "4.3.1"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"

dequal@^2.0.3:
  version "2.0.3"

detect-libc@^2.0.3, detect-libc@^2.0.4:
  version "2.0.4"

detect-newline@^3.1.0:
  version "3.1.0"

detect-node-es@^1.1.0:
  version "1.1.0"

doctrine@^2.1.0:
  version "2.1.0"
  dependencies:
    esutils "^2.0.2"

dom-accessibility-api@^0.5.9:
  version "0.5.16"

dom-accessibility-api@^0.6.3:
  version "0.6.3"

dom-helpers@^5.0.1:
  version "5.2.1"
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dotenv@^16.4.7:
  version "16.6.1"

dotenv@^4.0.0:
  version "4.0.0"

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"

ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  dependencies:
    safe-buffer "^5.0.1"

ejs@^3.1.10:
  version "3.1.10"
  dependencies:
    jake "^10.8.5"

electron-to-chromium@^1.5.173:
  version "1.5.178"

emittery@^0.13.1:
  version "0.13.1"

emoji-regex@^8.0.0:
  version "8.0.0"

emoji-regex@^9.2.2:
  version "9.2.2"

encoding@^0.1.12, encoding@~0.1.12:
  version "0.1.13"
  dependencies:
    iconv-lite "^0.6.2"

enhanced-resolve@^5.18.1:
  version "5.18.2"
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

entities@^4.4.0:
  version "4.5.0"

entities@^6.0.0:
  version "6.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.17.5, es-abstract@^1.23.2, es-abstract@^1.23.3, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9, es-abstract@^1.24.0:
  version "1.24.0"
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.3.0"
    get-proto "^1.0.1"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-negative-zero "^2.0.3"
    is-regex "^1.2.1"
    is-set "^2.0.3"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.1"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.4"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.4"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    stop-iteration-iterator "^1.1.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.19"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"

es-errors@^1.3.0:
  version "1.3.0"

es-iterator-helpers@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.6"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    iterator.prototype "^1.1.4"
    safe-array-concat "^1.1.3"

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3, es-set-tostringtag@^2.1.0:
  version "2.1.0"
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-shim-unscopables@^1.0.2, es-shim-unscopables@^1.1.0:
  version "1.1.0"
  dependencies:
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

esbuild@~0.25.0:
  version "0.25.5"
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.5"
    "@esbuild/android-arm" "0.25.5"
    "@esbuild/android-arm64" "0.25.5"
    "@esbuild/android-x64" "0.25.5"
    "@esbuild/darwin-arm64" "0.25.5"
    "@esbuild/darwin-x64" "0.25.5"
    "@esbuild/freebsd-arm64" "0.25.5"
    "@esbuild/freebsd-x64" "0.25.5"
    "@esbuild/linux-arm" "0.25.5"
    "@esbuild/linux-arm64" "0.25.5"
    "@esbuild/linux-ia32" "0.25.5"
    "@esbuild/linux-loong64" "0.25.5"
    "@esbuild/linux-mips64el" "0.25.5"
    "@esbuild/linux-ppc64" "0.25.5"
    "@esbuild/linux-riscv64" "0.25.5"
    "@esbuild/linux-s390x" "0.25.5"
    "@esbuild/linux-x64" "0.25.5"
    "@esbuild/netbsd-arm64" "0.25.5"
    "@esbuild/netbsd-x64" "0.25.5"
    "@esbuild/openbsd-arm64" "0.25.5"
    "@esbuild/openbsd-x64" "0.25.5"
    "@esbuild/sunos-x64" "0.25.5"
    "@esbuild/win32-arm64" "0.25.5"
    "@esbuild/win32-ia32" "0.25.5"
    "@esbuild/win32-x64" "0.25.5"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"

escape-string-regexp@^2.0.0:
  version "2.0.0"

escape-string-regexp@^4.0.0:
  version "4.0.0"

escape-string-regexp@~1.0.5:
  version "1.0.5"

eslint-config-next@15.2.3:
  version "15.2.3"
  dependencies:
    "@next/eslint-plugin-next" "15.2.3"
    "@rushstack/eslint-patch" "^1.10.3"
    "@typescript-eslint/eslint-plugin" "^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0"
    "@typescript-eslint/parser" "^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-import-resolver-typescript "^3.5.2"
    eslint-plugin-import "^2.31.0"
    eslint-plugin-jsx-a11y "^6.10.0"
    eslint-plugin-react "^7.37.0"
    eslint-plugin-react-hooks "^5.0.0"

eslint-config-prettier@^10.1.1:
  version "10.1.5"

eslint-import-resolver-node@^0.3.6, eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-import-resolver-typescript@^3.5.2:
  version "3.10.1"
  dependencies:
    "@nolyfill/is-core-module" "1.0.39"
    debug "^4.4.0"
    get-tsconfig "^4.10.0"
    is-bun-module "^2.0.0"
    stable-hash "^0.0.5"
    tinyglobby "^0.2.13"
    unrs-resolver "^1.6.2"

eslint-module-utils@^2.12.1:
  version "2.12.1"
  dependencies:
    debug "^3.2.7"

eslint-plugin-import@*, eslint-plugin-import@^2.31.0:
  version "2.32.0"
  dependencies:
    "@rtsao/scc" "^1.1.0"
    array-includes "^3.1.9"
    array.prototype.findlastindex "^1.2.6"
    array.prototype.flat "^1.3.3"
    array.prototype.flatmap "^1.3.3"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.12.1"
    hasown "^2.0.2"
    is-core-module "^2.16.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    object.groupby "^1.0.3"
    object.values "^1.2.1"
    semver "^6.3.1"
    string.prototype.trimend "^1.0.9"
    tsconfig-paths "^3.15.0"

eslint-plugin-jsx-a11y@^6.10.0:
  version "6.10.2"
  dependencies:
    aria-query "^5.3.2"
    array-includes "^3.1.8"
    array.prototype.flatmap "^1.3.2"
    ast-types-flow "^0.0.8"
    axe-core "^4.10.0"
    axobject-query "^4.1.0"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    hasown "^2.0.2"
    jsx-ast-utils "^3.3.5"
    language-tags "^1.0.9"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    safe-regex-test "^1.0.3"
    string.prototype.includes "^2.0.1"

eslint-plugin-react-hooks@^5.0.0:
  version "5.2.0"

eslint-plugin-react@^7.37.0:
  version "7.37.5"
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.3"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.2.1"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.9"
    object.fromentries "^2.0.8"
    object.values "^1.2.1"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.12"
    string.prototype.repeat "^1.0.0"

eslint-scope@^8.4.0:
  version "8.4.0"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.4.3:
  version "3.4.3"

eslint-visitor-keys@^4.2.1:
  version "4.2.1"

eslint@*, "eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^7.23.0 || ^8.0.0 || ^9.0.0", "eslint@^8.57.0 || ^9.0.0", eslint@^9.30.0, eslint@>=7.0.0:
  version "9.32.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.12.1"
    "@eslint/config-array" "^0.21.0"
    "@eslint/config-helpers" "^0.3.0"
    "@eslint/core" "^0.15.0"
    "@eslint/eslintrc" "^3.3.1"
    "@eslint/js" "9.32.0"
    "@eslint/plugin-kit" "^0.3.4"
    "@humanfs/node" "^0.16.6"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@humanwhocodes/retry" "^0.4.2"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.6"
    debug "^4.3.2"
    escape-string-regexp "^4.0.0"
    eslint-scope "^8.4.0"
    eslint-visitor-keys "^4.2.1"
    espree "^10.4.0"
    esquery "^1.5.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^8.0.0"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"

espree@^10.0.1, espree@^10.4.0:
  version "10.4.0"
  dependencies:
    acorn "^8.15.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^4.2.1"

esprima@^4.0.0:
  version "4.0.1"

esquery@^1.5.0:
  version "1.6.0"
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"

esutils@^2.0.2:
  version "2.0.3"

eventemitter3@^4.0.1:
  version "4.0.7"

eventemitter3@^5.0.1:
  version "5.0.1"

execa@^5.1.1:
  version "5.1.1"
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit-x@^0.2.2:
  version "0.2.2"

expect@^30.0.0, expect@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/expect-utils" "30.0.5"
    "@jest/get-type" "30.0.1"
    jest-matcher-utils "30.0.5"
    jest-message-util "30.0.5"
    jest-mock "30.0.5"
    jest-util "30.0.5"

extend@~3.0.0:
  version "3.0.2"

external-editor@^3.1.0:
  version "3.1.0"
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"

fast-equals@^5.0.1:
  version "5.2.2"

fast-glob@^3.3.2:
  version "3.3.3"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-glob@3.3.1:
  version "3.3.1"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0, fast-json-stable-stringify@2.x:
  version "2.1.0"

fast-levenshtein@^2.0.6:
  version "2.0.6"

fast-xml-parser@^4.4.1:
  version "4.5.3"
  dependencies:
    strnum "^1.1.1"

fast-xml-parser@5.2.5:
  version "5.2.5"
  dependencies:
    strnum "^2.1.0"

fastq@^1.6.0:
  version "1.19.1"
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.2:
  version "2.0.2"
  dependencies:
    bser "2.1.1"

fdir@^6.4.4:
  version "6.4.6"

fetch-blob@^3.1.2, fetch-blob@^3.1.4:
  version "3.2.0"
  dependencies:
    node-domexception "^1.0.0"
    web-streams-polyfill "^3.0.3"

file-entry-cache@^8.0.0:
  version "8.0.0"
  dependencies:
    flat-cache "^4.0.0"

filelist@^1.0.4:
  version "1.0.4"
  dependencies:
    minimatch "^5.0.1"

fill-range@^7.1.1:
  version "7.1.1"
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^1.1.0:
  version "1.1.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^4.0.0:
  version "4.0.1"
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.4"

flatted@^3.2.9:
  version "3.3.3"

follow-redirects@^1.15.6:
  version "1.15.9"

for-each@^0.3.3, for-each@^0.3.5:
  version "0.3.5"
  dependencies:
    is-callable "^1.2.7"

foreground-child@^3.1.0:
  version "3.3.1"
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

form-data@^4.0.0, form-data@^4.0.4:
  version "4.0.4"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    hasown "^2.0.2"
    mime-types "^2.1.12"

formdata-polyfill@^4.0.10:
  version "4.0.10"
  dependencies:
    fetch-blob "^3.1.2"

frac@~1.1.2:
  version "1.1.2"

framer-motion@^12.6.2:
  version "12.20.1"
  dependencies:
    motion-dom "^12.20.1"
    motion-utils "^12.19.0"
    tslib "^2.4.0"

fs.realpath@^1.0.0:
  version "1.0.0"

fsevents@^2.3.3, fsevents@~2.3.3:
  version "2.3.3"

fsevents@2.3.2:
  version "2.3.2"

function-bind@^1.1.2:
  version "1.1.2"

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functions-have-names@^1.2.3:
  version "1.2.3"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"

get-caller-file@^2.0.5:
  version "2.0.5"

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.2.7, get-intrinsic@^1.3.0:
  version "1.3.0"
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-nonce@^1.0.0:
  version "1.0.1"

get-package-type@^0.1.0:
  version "0.1.0"

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stream@^6.0.0:
  version "6.0.1"

get-symbol-description@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

get-tsconfig@^4.10.0, get-tsconfig@^4.7.5:
  version "4.10.1"
  dependencies:
    resolve-pkg-maps "^1.0.0"

glob-parent@^5.1.2:
  version "5.1.2"
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  dependencies:
    is-glob "^4.0.3"

glob@^10.3.10:
  version "10.4.5"
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.4:
  version "7.2.3"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"

globals@^14.0.0:
  version "14.0.0"

globalthis@^1.0.4:
  version "1.0.4"
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"

graceful-fs@^4.2.11, graceful-fs@^4.2.4:
  version "4.2.11"

graphemer@^1.4.0:
  version "1.4.0"

graphql@^16.8.1:
  version "16.11.0"

has-bigints@^1.0.2:
  version "1.1.0"

has-flag@^4.0.0:
  version "4.0.0"

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"

has-tostringtag@^1.0.2:
  version "1.0.2"
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  dependencies:
    function-bind "^1.1.2"

headers-polyfill@^4.0.2:
  version "4.0.3"

html-encoding-sniffer@^4.0.0:
  version "4.0.0"
  dependencies:
    whatwg-encoding "^3.1.1"

html-escaper@^2.0.0:
  version "2.0.2"

http-proxy-agent@^7.0.2:
  version "7.0.2"
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

https-proxy-agent@^7.0.6:
  version "7.0.6"
  dependencies:
    agent-base "^7.1.2"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"

iconv-lite@^0.4.24:
  version "0.4.24"
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.2, iconv-lite@0.6.3:
  version "0.6.3"
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ignore@^5.2.0:
  version "5.3.2"

ignore@^7.0.0:
  version "7.0.5"

immer@^10.1.1, immer@>=9.0.6:
  version "10.1.1"

import-fresh@^3.2.1:
  version "3.3.1"
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.2.0:
  version "3.2.0"
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"

indent-string@^4.0.0:
  version "4.0.0"

inflight@^1.0.4:
  version "1.0.6"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@2:
  version "2.0.4"

inquirer@^12.7.0:
  version "12.7.0"
  dependencies:
    "@inquirer/core" "^10.1.14"
    "@inquirer/prompts" "^7.6.0"
    "@inquirer/type" "^3.0.7"
    ansi-escapes "^4.3.2"
    mute-stream "^2.0.0"
    run-async "^4.0.4"
    rxjs "^7.8.2"

internal-slot@^1.1.0:
  version "1.1.0"
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

"internmap@1 - 2":
  version "2.0.3"

ipaddr.js@^2.0.1:
  version "2.2.0"

is-arguments@^1.0.4:
  version "1.2.0"
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.2.1:
  version "0.2.1"

is-arrayish@^0.3.1:
  version "0.3.2"

is-async-function@^2.0.0:
  version "2.1.1"
  dependencies:
    async-function "^1.0.0"
    call-bound "^1.0.3"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-bigint@^1.1.0:
  version "1.1.0"
  dependencies:
    has-bigints "^1.0.2"

is-boolean-object@^1.2.1:
  version "1.2.2"
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-bun-module@^2.0.0:
  version "2.0.0"
  dependencies:
    semver "^7.7.1"

is-callable@^1.2.7:
  version "1.2.7"

is-core-module@^2.13.0, is-core-module@^2.16.0, is-core-module@^2.16.1:
  version "2.16.1"
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-extglob@^2.1.1:
  version "2.1.1"

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"

is-generator-fn@^2.1.0:
  version "2.1.0"

is-generator-function@^1.0.10, is-generator-function@^1.0.7:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3:
  version "4.0.3"
  dependencies:
    is-extglob "^2.1.1"

is-map@^2.0.3:
  version "2.0.3"

is-negative-zero@^2.0.3:
  version "2.0.3"

is-node-process@^1.2.0:
  version "1.2.0"

is-number-object@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^7.0.0:
  version "7.0.0"

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"

is-regex@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-set@^2.0.3:
  version "2.0.3"

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  dependencies:
    call-bound "^1.0.3"

is-stream@^2.0.0:
  version "2.0.1"

is-string@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15, is-typed-array@^1.1.3:
  version "1.1.15"
  dependencies:
    which-typed-array "^1.1.16"

is-weakmap@^2.0.2:
  version "2.0.2"

is-weakref@^1.0.2, is-weakref@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"

is-weakset@^2.0.3:
  version "2.0.4"
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

isarray@^2.0.5:
  version "2.0.5"

isexe@^2.0.0:
  version "2.0.0"

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.2"

istanbul-lib-instrument@^6.0.0, istanbul-lib-instrument@^6.0.2:
  version "6.0.3"
  dependencies:
    "@babel/core" "^7.23.9"
    "@babel/parser" "^7.23.9"
    "@istanbuljs/schema" "^0.1.3"
    istanbul-lib-coverage "^3.2.0"
    semver "^7.5.4"

istanbul-lib-report@^3.0.0:
  version "3.0.1"
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^5.0.0:
  version "5.0.6"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.23"
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"

istanbul-reports@^3.1.3:
  version "3.1.7"
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

iterator.prototype@^1.1.4:
  version "1.1.5"
  dependencies:
    define-data-property "^1.1.4"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    get-proto "^1.0.0"
    has-symbols "^1.1.0"
    set-function-name "^2.0.2"

jackspeak@^3.1.2:
  version "3.4.3"
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jake@^10.8.5:
  version "10.9.2"
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.4"
    minimatch "^3.1.2"

jest-changed-files@30.0.5:
  version "30.0.5"
  dependencies:
    execa "^5.1.1"
    jest-util "30.0.5"
    p-limit "^3.1.0"

jest-circus@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/environment" "30.0.5"
    "@jest/expect" "30.0.5"
    "@jest/test-result" "30.0.5"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    chalk "^4.1.2"
    co "^4.6.0"
    dedent "^1.6.0"
    is-generator-fn "^2.1.0"
    jest-each "30.0.5"
    jest-matcher-utils "30.0.5"
    jest-message-util "30.0.5"
    jest-runtime "30.0.5"
    jest-snapshot "30.0.5"
    jest-util "30.0.5"
    p-limit "^3.1.0"
    pretty-format "30.0.5"
    pure-rand "^7.0.0"
    slash "^3.0.0"
    stack-utils "^2.0.6"

jest-cli@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/core" "30.0.5"
    "@jest/test-result" "30.0.5"
    "@jest/types" "30.0.5"
    chalk "^4.1.2"
    exit-x "^0.2.2"
    import-local "^3.2.0"
    jest-config "30.0.5"
    jest-util "30.0.5"
    jest-validate "30.0.5"
    yargs "^17.7.2"

jest-config@30.0.5:
  version "30.0.5"
  dependencies:
    "@babel/core" "^7.27.4"
    "@jest/get-type" "30.0.1"
    "@jest/pattern" "30.0.1"
    "@jest/test-sequencer" "30.0.5"
    "@jest/types" "30.0.5"
    babel-jest "30.0.5"
    chalk "^4.1.2"
    ci-info "^4.2.0"
    deepmerge "^4.3.1"
    glob "^10.3.10"
    graceful-fs "^4.2.11"
    jest-circus "30.0.5"
    jest-docblock "30.0.1"
    jest-environment-node "30.0.5"
    jest-regex-util "30.0.1"
    jest-resolve "30.0.5"
    jest-runner "30.0.5"
    jest-util "30.0.5"
    jest-validate "30.0.5"
    micromatch "^4.0.8"
    parse-json "^5.2.0"
    pretty-format "30.0.5"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/diff-sequences" "30.0.1"
    "@jest/get-type" "30.0.1"
    chalk "^4.1.2"
    pretty-format "30.0.5"

jest-docblock@30.0.1:
  version "30.0.1"
  dependencies:
    detect-newline "^3.1.0"

jest-each@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/get-type" "30.0.1"
    "@jest/types" "30.0.5"
    chalk "^4.1.2"
    jest-util "30.0.5"
    pretty-format "30.0.5"

jest-environment-jsdom@^30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/environment" "30.0.5"
    "@jest/environment-jsdom-abstract" "30.0.5"
    "@types/jsdom" "^21.1.7"
    "@types/node" "*"
    jsdom "^26.1.0"

jest-environment-node@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/environment" "30.0.5"
    "@jest/fake-timers" "30.0.5"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    jest-mock "30.0.5"
    jest-util "30.0.5"
    jest-validate "30.0.5"

jest-haste-map@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/types" "30.0.5"
    "@types/node" "*"
    anymatch "^3.1.3"
    fb-watchman "^2.0.2"
    graceful-fs "^4.2.11"
    jest-regex-util "30.0.1"
    jest-util "30.0.5"
    jest-worker "30.0.5"
    micromatch "^4.0.8"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.3"

jest-leak-detector@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/get-type" "30.0.1"
    pretty-format "30.0.5"

jest-matcher-utils@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/get-type" "30.0.1"
    chalk "^4.1.2"
    jest-diff "30.0.5"
    pretty-format "30.0.5"

jest-message-util@30.0.5:
  version "30.0.5"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@jest/types" "30.0.5"
    "@types/stack-utils" "^2.0.3"
    chalk "^4.1.2"
    graceful-fs "^4.2.11"
    micromatch "^4.0.8"
    pretty-format "30.0.5"
    slash "^3.0.0"
    stack-utils "^2.0.6"

jest-mock@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/types" "30.0.5"
    "@types/node" "*"
    jest-util "30.0.5"

jest-pnp-resolver@^1.2.3:
  version "1.2.3"

jest-regex-util@30.0.1:
  version "30.0.1"

jest-resolve-dependencies@30.0.5:
  version "30.0.5"
  dependencies:
    jest-regex-util "30.0.1"
    jest-snapshot "30.0.5"

jest-resolve@*, jest-resolve@30.0.5:
  version "30.0.5"
  dependencies:
    chalk "^4.1.2"
    graceful-fs "^4.2.11"
    jest-haste-map "30.0.5"
    jest-pnp-resolver "^1.2.3"
    jest-util "30.0.5"
    jest-validate "30.0.5"
    slash "^3.0.0"
    unrs-resolver "^1.7.11"

jest-runner@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/console" "30.0.5"
    "@jest/environment" "30.0.5"
    "@jest/test-result" "30.0.5"
    "@jest/transform" "30.0.5"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    chalk "^4.1.2"
    emittery "^0.13.1"
    exit-x "^0.2.2"
    graceful-fs "^4.2.11"
    jest-docblock "30.0.1"
    jest-environment-node "30.0.5"
    jest-haste-map "30.0.5"
    jest-leak-detector "30.0.5"
    jest-message-util "30.0.5"
    jest-resolve "30.0.5"
    jest-runtime "30.0.5"
    jest-util "30.0.5"
    jest-watcher "30.0.5"
    jest-worker "30.0.5"
    p-limit "^3.1.0"
    source-map-support "0.5.13"

jest-runtime@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/environment" "30.0.5"
    "@jest/fake-timers" "30.0.5"
    "@jest/globals" "30.0.5"
    "@jest/source-map" "30.0.1"
    "@jest/test-result" "30.0.5"
    "@jest/transform" "30.0.5"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    chalk "^4.1.2"
    cjs-module-lexer "^2.1.0"
    collect-v8-coverage "^1.0.2"
    glob "^10.3.10"
    graceful-fs "^4.2.11"
    jest-haste-map "30.0.5"
    jest-message-util "30.0.5"
    jest-mock "30.0.5"
    jest-regex-util "30.0.1"
    jest-resolve "30.0.5"
    jest-snapshot "30.0.5"
    jest-util "30.0.5"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-snapshot@30.0.5:
  version "30.0.5"
  dependencies:
    "@babel/core" "^7.27.4"
    "@babel/generator" "^7.27.5"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/plugin-syntax-typescript" "^7.27.1"
    "@babel/types" "^7.27.3"
    "@jest/expect-utils" "30.0.5"
    "@jest/get-type" "30.0.1"
    "@jest/snapshot-utils" "30.0.5"
    "@jest/transform" "30.0.5"
    "@jest/types" "30.0.5"
    babel-preset-current-node-syntax "^1.1.0"
    chalk "^4.1.2"
    expect "30.0.5"
    graceful-fs "^4.2.11"
    jest-diff "30.0.5"
    jest-matcher-utils "30.0.5"
    jest-message-util "30.0.5"
    jest-util "30.0.5"
    pretty-format "30.0.5"
    semver "^7.7.2"
    synckit "^0.11.8"

"jest-util@^29.0.0 || ^30.0.0", jest-util@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/types" "30.0.5"
    "@types/node" "*"
    chalk "^4.1.2"
    ci-info "^4.2.0"
    graceful-fs "^4.2.11"
    picomatch "^4.0.2"

jest-validate@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/get-type" "30.0.1"
    "@jest/types" "30.0.5"
    camelcase "^6.3.0"
    chalk "^4.1.2"
    leven "^3.1.0"
    pretty-format "30.0.5"

jest-watcher@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/test-result" "30.0.5"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    ansi-escapes "^4.3.2"
    chalk "^4.1.2"
    emittery "^0.13.1"
    jest-util "30.0.5"
    string-length "^4.0.2"

jest-worker@30.0.5:
  version "30.0.5"
  dependencies:
    "@types/node" "*"
    "@ungap/structured-clone" "^1.3.0"
    jest-util "30.0.5"
    merge-stream "^2.0.0"
    supports-color "^8.1.1"

"jest@^29.0.0 || ^30.0.0", jest@^30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/core" "30.0.5"
    "@jest/types" "30.0.5"
    import-local "^3.2.0"
    jest-cli "30.0.5"

jiti@*, jiti@^2.4.2:
  version "2.4.2"

jiti@2.4.2:
  version "2.4.2"

jose@^4.15.5:
  version "4.15.9"

jose@^4.15.9:
  version "4.15.9"

jose@^6.0.10:
  version "6.0.11"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"

js-yaml@^3.13.1:
  version "3.14.1"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  dependencies:
    argparse "^2.0.1"

jsdom@*, jsdom@^26.1.0:
  version "26.1.0"
  dependencies:
    cssstyle "^4.2.1"
    data-urls "^5.0.0"
    decimal.js "^10.5.0"
    html-encoding-sniffer "^4.0.0"
    http-proxy-agent "^7.0.2"
    https-proxy-agent "^7.0.6"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.16"
    parse5 "^7.2.1"
    rrweb-cssom "^0.8.0"
    saxes "^6.0.0"
    symbol-tree "^3.2.4"
    tough-cookie "^5.1.1"
    w3c-xmlserializer "^5.0.0"
    webidl-conversions "^7.0.0"
    whatwg-encoding "^3.1.1"
    whatwg-mimetype "^4.0.0"
    whatwg-url "^14.1.1"
    ws "^8.18.0"
    xml-name-validator "^5.0.0"

jsesc@^3.0.2:
  version "3.1.0"

json-buffer@3.0.1:
  version "3.0.1"

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"

json-schema-traverse@^0.4.1:
  version "0.4.1"

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"

json5@^1.0.2:
  version "1.0.2"
  dependencies:
    minimist "^1.2.0"

json5@^2.2.3:
  version "2.2.3"

jsonwebtoken@^9.0.2:
  version "9.0.2"
  dependencies:
    jws "^3.2.2"
    lodash.includes "^4.3.0"
    lodash.isboolean "^3.0.3"
    lodash.isinteger "^4.0.4"
    lodash.isnumber "^3.0.3"
    lodash.isplainobject "^4.0.6"
    lodash.isstring "^4.0.1"
    lodash.once "^4.0.0"
    ms "^2.1.1"
    semver "^7.5.4"

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.5:
  version "3.3.5"
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

jwa@^1.4.1:
  version "1.4.2"
  dependencies:
    buffer-equal-constant-time "^1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^3.2.2:
  version "3.2.2"
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

keyv@^4.5.4:
  version "4.5.4"
  dependencies:
    json-buffer "3.0.1"

language-subtag-registry@^0.3.20:
  version "0.3.23"

language-tags@^1.0.9:
  version "1.0.9"
  dependencies:
    language-subtag-registry "^0.3.20"

leven@^3.1.0:
  version "3.1.0"

levn@^0.4.1:
  version "0.4.1"
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lightningcss-darwin-arm64@1.30.1:
  version "1.30.1"

lightningcss@1.30.1:
  version "1.30.1"
  dependencies:
    detect-libc "^2.0.3"
  optionalDependencies:
    lightningcss-darwin-arm64 "1.30.1"
    lightningcss-darwin-x64 "1.30.1"
    lightningcss-freebsd-x64 "1.30.1"
    lightningcss-linux-arm-gnueabihf "1.30.1"
    lightningcss-linux-arm64-gnu "1.30.1"
    lightningcss-linux-arm64-musl "1.30.1"
    lightningcss-linux-x64-gnu "1.30.1"
    lightningcss-linux-x64-musl "1.30.1"
    lightningcss-win32-arm64-msvc "1.30.1"
    lightningcss-win32-x64-msvc "1.30.1"

lines-and-columns@^1.1.6:
  version "1.2.4"

linkify-it@^5.0.0:
  version "5.0.0"
  dependencies:
    uc.micro "^2.0.0"

linkifyjs@^4.2.0:
  version "4.3.1"

locate-path@^5.0.0:
  version "5.0.0"
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  dependencies:
    p-locate "^5.0.0"

lodash.chunk@^4.2.0:
  version "4.2.0"

lodash.includes@^4.3.0:
  version "4.3.0"

lodash.isboolean@^3.0.3:
  version "3.0.3"

lodash.isinteger@^4.0.4:
  version "4.0.4"

lodash.isnumber@^3.0.3:
  version "3.0.3"

lodash.isplainobject@^4.0.6:
  version "4.0.6"

lodash.isstring@^4.0.1:
  version "4.0.1"

lodash.memoize@^4.1.2:
  version "4.1.2"

lodash.merge@^4.6.2:
  version "4.6.2"

lodash.once@^4.0.0:
  version "4.1.1"

lodash@^4.17.21:
  version "4.17.21"

loose-envify@^1.4.0:
  version "1.4.0"
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^10.2.0, lru-cache@^10.4.3:
  version "10.4.3"

lru-cache@^5.1.1:
  version "5.1.1"
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  dependencies:
    yallist "^4.0.0"

lucide-react@^0.483.0:
  version "0.483.0"

lz-string@^1.5.0:
  version "1.5.0"

magic-string@^0.30.17:
  version "0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

mailparser@^0.6.1:
  version "0.6.2"
  dependencies:
    encoding "^0.1.12"
    mime "^1.3.4"
    mimelib "^0.3.0"
    uue "^3.1.0"

make-dir@^4.0.0:
  version "4.0.0"
  dependencies:
    semver "^7.5.3"

make-error@^1.3.6:
  version "1.3.6"

makeerror@1.0.12:
  version "1.0.12"
  dependencies:
    tmpl "1.0.5"

markdown-it@^14.0.0:
  version "14.1.0"
  dependencies:
    argparse "^2.0.1"
    entities "^4.4.0"
    linkify-it "^5.0.0"
    mdurl "^2.0.0"
    punycode.js "^2.3.1"
    uc.micro "^2.1.0"

math-intrinsics@^1.1.0:
  version "1.1.0"

mdurl@^2.0.0:
  version "2.0.0"

merge-stream@^2.0.0:
  version "2.0.0"

merge2@^1.3.0:
  version "1.4.1"

micromatch@^4.0.4, micromatch@^4.0.8:
  version "4.0.8"
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"

mime-types@^2.1.12, mime-types@^2.1.35:
  version "2.1.35"
  dependencies:
    mime-db "1.52.0"

mime@^1.3.4:
  version "1.6.0"

mimelib@^0.3.0:
  version "0.3.1"
  dependencies:
    addressparser "~1.0.1"
    encoding "~0.1.12"

mimic-fn@^2.1.0:
  version "2.1.0"

min-indent@^1.0.0:
  version "1.0.1"

minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.4:
  version "9.0.5"
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"

minio@^8.0.5:
  version "8.0.5"
  dependencies:
    async "^3.2.4"
    block-stream2 "^2.1.0"
    browser-or-node "^2.1.1"
    buffer-crc32 "^1.0.0"
    eventemitter3 "^5.0.1"
    fast-xml-parser "^4.4.1"
    ipaddr.js "^2.0.1"
    lodash "^4.17.21"
    mime-types "^2.1.35"
    query-string "^7.1.3"
    stream-json "^1.8.0"
    through2 "^4.0.2"
    web-encoding "^1.1.5"
    xml2js "^0.5.0 || ^0.6.2"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.0.4, minipass@^7.1.2:
  version "7.1.2"

minizlib@^3.0.1:
  version "3.0.2"
  dependencies:
    minipass "^7.1.2"

mkdirp@^3.0.1:
  version "3.0.1"

motion-dom@^12.20.1:
  version "12.20.1"
  dependencies:
    motion-utils "^12.19.0"

motion-utils@^12.19.0:
  version "12.19.0"

ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"

ms@2.0.0:
  version "2.0.0"

msw@^2.10.4:
  version "2.10.4"
  dependencies:
    "@bundled-es-modules/cookie" "^2.0.1"
    "@bundled-es-modules/statuses" "^1.0.1"
    "@bundled-es-modules/tough-cookie" "^0.1.6"
    "@inquirer/confirm" "^5.0.0"
    "@mswjs/interceptors" "^0.39.1"
    "@open-draft/deferred-promise" "^2.2.0"
    "@open-draft/until" "^2.1.0"
    "@types/cookie" "^0.6.0"
    "@types/statuses" "^2.0.4"
    graphql "^16.8.1"
    headers-polyfill "^4.0.2"
    is-node-process "^1.2.0"
    outvariant "^1.4.3"
    path-to-regexp "^6.3.0"
    picocolors "^1.1.1"
    strict-event-emitter "^0.5.1"
    type-fest "^4.26.1"
    yargs "^17.7.2"

mute-stream@^2.0.0:
  version "2.0.0"

nanoid@^3.3.11, nanoid@^3.3.6:
  version "3.3.11"

napi-postinstall@^0.2.4:
  version "0.2.5"

natural-compare@^1.4.0:
  version "1.4.0"

next-auth@^4, next-auth@^4.24.11:
  version "4.24.11"
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@panva/hkdf" "^1.0.2"
    cookie "^0.7.0"
    jose "^4.15.5"
    oauth "^0.9.15"
    openid-client "^5.4.0"
    preact "^10.6.3"
    preact-render-to-string "^5.1.19"
    uuid "^8.3.2"

next-themes@^0.4.6:
  version "0.4.6"

"next@^12.2.5 || ^13 || ^14 || ^15", next@^15.4.2:
  version "15.4.2"
  dependencies:
    "@next/env" "15.4.2"
    "@swc/helpers" "0.5.15"
    caniuse-lite "^1.0.30001579"
    postcss "8.4.31"
    styled-jsx "5.1.6"
  optionalDependencies:
    "@next/swc-darwin-arm64" "15.4.2"
    "@next/swc-darwin-x64" "15.4.2"
    "@next/swc-linux-arm64-gnu" "15.4.2"
    "@next/swc-linux-arm64-musl" "15.4.2"
    "@next/swc-linux-x64-gnu" "15.4.2"
    "@next/swc-linux-x64-musl" "15.4.2"
    "@next/swc-win32-arm64-msvc" "15.4.2"
    "@next/swc-win32-x64-msvc" "15.4.2"
    sharp "^0.34.3"

node-cache@^5.1.2:
  version "5.1.2"
  dependencies:
    clone "2.x"

node-domexception@^1.0.0:
  version "1.0.0"

node-fetch@^3.3.2:
  version "3.3.2"
  dependencies:
    data-uri-to-buffer "^4.0.0"
    fetch-blob "^3.1.4"
    formdata-polyfill "^4.0.10"

node-int64@^0.4.0:
  version "0.4.0"

node-releases@^2.0.19:
  version "2.0.19"

nodemailer@^6.10.1, nodemailer@^6.6.5:
  version "6.10.1"

normalize-path@^3.0.0:
  version "3.0.0"

npm-run-path@^4.0.1:
  version "4.0.1"
  dependencies:
    path-key "^3.0.0"

nprogress@^0.2.0:
  version "0.2.0"

nwsapi@^2.2.16:
  version "2.2.20"

oauth@^0.9.15:
  version "0.9.15"

object-assign@^4.1.1:
  version "4.1.1"

object-hash@^2.2.0:
  version "2.2.0"

object-inspect@^1.13.3, object-inspect@^1.13.4:
  version "1.13.4"

object-keys@^1.1.1:
  version "1.1.1"

object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.entries@^1.1.9:
  version "1.1.9"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-object-atoms "^1.1.1"

object.fromentries@^2.0.8:
  version "2.0.8"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.groupby@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"

object.values@^1.1.6, object.values@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

oidc-token-hash@^5.0.3:
  version "5.1.0"

once@^1.3.0:
  version "1.4.0"
  dependencies:
    wrappy "1"

onetime@^5.1.2:
  version "5.1.2"
  dependencies:
    mimic-fn "^2.1.0"

openid-client@^5.4.0:
  version "5.7.1"
  dependencies:
    jose "^4.15.9"
    lru-cache "^6.0.0"
    object-hash "^2.2.0"
    oidc-token-hash "^5.0.3"

optionator@^0.9.3:
  version "0.9.4"
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

orderedmap@^2.0.0:
  version "2.1.1"

os-tmpdir@~1.0.2:
  version "1.0.2"

outvariant@^1.4.0, outvariant@^1.4.3:
  version "1.4.3"

own-keys@^1.0.1:
  version "1.0.1"
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

oxlint@^1.2.0:
  version "1.5.0"
  optionalDependencies:
    "@oxlint/darwin-arm64" "1.5.0"
    "@oxlint/darwin-x64" "1.5.0"
    "@oxlint/linux-arm64-gnu" "1.5.0"
    "@oxlint/linux-arm64-musl" "1.5.0"
    "@oxlint/linux-x64-gnu" "1.5.0"
    "@oxlint/linux-x64-musl" "1.5.0"
    "@oxlint/win32-arm64" "1.5.0"
    "@oxlint/win32-x64" "1.5.0"

p-limit@^2.2.0:
  version "2.3.0"
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2, p-limit@^3.1.0:
  version "3.1.0"
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  dependencies:
    p-limit "^3.0.2"

p-try@^2.0.0:
  version "2.2.0"

package-json-from-dist@^1.0.0:
  version "1.0.1"

parent-module@^1.0.0:
  version "1.0.1"
  dependencies:
    callsites "^3.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5@^7.0.0, parse5@^7.2.1:
  version "7.3.0"
  dependencies:
    entities "^6.0.0"

path-exists@^4.0.0:
  version "4.0.0"

path-is-absolute@^1.0.0:
  version "1.0.1"

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"

path-parse@^1.0.7:
  version "1.0.7"

path-scurry@^1.11.1:
  version "1.11.1"
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@^6.3.0:
  version "6.3.0"

picocolors@^1.0.0, picocolors@^1.1.1:
  version "1.1.1"

picomatch@^2.0.4:
  version "2.3.1"

picomatch@^2.3.1:
  version "2.3.1"

"picomatch@^3 || ^4", picomatch@^4.0.2:
  version "4.0.2"

pirates@^4.0.7:
  version "4.0.7"

pkg-dir@^4.2.0:
  version "4.2.0"
  dependencies:
    find-up "^4.0.0"

playwright-core@1.54.1:
  version "1.54.1"

playwright@1.54.1:
  version "1.54.1"
  dependencies:
    playwright-core "1.54.1"
  optionalDependencies:
    fsevents "2.3.2"

possible-typed-array-names@^1.0.0:
  version "1.1.0"

postcss@^8.4.41:
  version "8.5.6"
  dependencies:
    nanoid "^3.3.11"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

postcss@8.4.31:
  version "8.4.31"
  dependencies:
    nanoid "^3.3.6"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

preact-render-to-string@^5.1.19:
  version "5.2.6"
  dependencies:
    pretty-format "^3.8.0"

preact@^10.6.3, preact@>=10:
  version "10.26.9"

prelude-ls@^1.2.1:
  version "1.2.1"

prettier@^3.5.3:
  version "3.6.2"

pretty-format@^27.0.2:
  version "27.5.1"
  dependencies:
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^17.0.1"

pretty-format@^3.8.0:
  version "3.8.0"

pretty-format@^30.0.0, pretty-format@30.0.5:
  version "30.0.5"
  dependencies:
    "@jest/schemas" "30.0.5"
    ansi-styles "^5.2.0"
    react-is "^18.3.1"

prisma@*, prisma@^6.12.0:
  version "6.12.0"
  dependencies:
    "@prisma/config" "6.12.0"
    "@prisma/engines" "6.12.0"

prop-types@^15.6.2, prop-types@^15.8.1:
  version "15.8.1"
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

prosemirror-changeset@^2.3.0:
  version "2.3.1"
  dependencies:
    prosemirror-transform "^1.0.0"

prosemirror-collab@^1.3.1:
  version "1.3.1"
  dependencies:
    prosemirror-state "^1.0.0"

prosemirror-commands@^1.0.0, prosemirror-commands@^1.6.2:
  version "1.7.1"
  dependencies:
    prosemirror-model "^1.0.0"
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.10.2"

prosemirror-dropcursor@^1.8.1:
  version "1.8.2"
  dependencies:
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.1.0"
    prosemirror-view "^1.1.0"

prosemirror-gapcursor@^1.3.2:
  version "1.3.2"
  dependencies:
    prosemirror-keymap "^1.0.0"
    prosemirror-model "^1.0.0"
    prosemirror-state "^1.0.0"
    prosemirror-view "^1.0.0"

prosemirror-history@^1.0.0, prosemirror-history@^1.4.1:
  version "1.4.1"
  dependencies:
    prosemirror-state "^1.2.2"
    prosemirror-transform "^1.0.0"
    prosemirror-view "^1.31.0"
    rope-sequence "^1.3.0"

prosemirror-inputrules@^1.4.0:
  version "1.5.0"
  dependencies:
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.0.0"

prosemirror-keymap@^1.0.0, prosemirror-keymap@^1.2.2:
  version "1.2.3"
  dependencies:
    prosemirror-state "^1.0.0"
    w3c-keyname "^2.2.0"

prosemirror-markdown@^1.13.1:
  version "1.13.2"
  dependencies:
    "@types/markdown-it" "^14.0.0"
    markdown-it "^14.0.0"
    prosemirror-model "^1.25.0"

prosemirror-menu@^1.2.4:
  version "1.2.5"
  dependencies:
    crelt "^1.0.0"
    prosemirror-commands "^1.0.0"
    prosemirror-history "^1.0.0"
    prosemirror-state "^1.0.0"

prosemirror-model@^1.0.0, prosemirror-model@^1.20.0, prosemirror-model@^1.21.0, prosemirror-model@^1.22.1, prosemirror-model@^1.24.1, prosemirror-model@^1.25.0:
  version "1.25.2"
  dependencies:
    orderedmap "^2.0.0"

prosemirror-schema-basic@^1.2.3:
  version "1.2.4"
  dependencies:
    prosemirror-model "^1.25.0"

prosemirror-schema-list@^1.5.0:
  version "1.5.1"
  dependencies:
    prosemirror-model "^1.0.0"
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.7.3"

prosemirror-state@^1.0.0, prosemirror-state@^1.2.2, prosemirror-state@^1.4.2, prosemirror-state@^1.4.3:
  version "1.4.3"
  dependencies:
    prosemirror-model "^1.0.0"
    prosemirror-transform "^1.0.0"
    prosemirror-view "^1.27.0"

prosemirror-tables@^1.6.4:
  version "1.7.1"
  dependencies:
    prosemirror-keymap "^1.2.2"
    prosemirror-model "^1.25.0"
    prosemirror-state "^1.4.3"
    prosemirror-transform "^1.10.3"
    prosemirror-view "^1.39.1"

prosemirror-trailing-node@^3.0.0:
  version "3.0.0"
  dependencies:
    "@remirror/core-constants" "3.0.0"
    escape-string-regexp "^4.0.0"

prosemirror-transform@^1.0.0, prosemirror-transform@^1.1.0, prosemirror-transform@^1.10.2, prosemirror-transform@^1.10.3, prosemirror-transform@^1.7.3:
  version "1.10.4"
  dependencies:
    prosemirror-model "^1.21.0"

prosemirror-view@^1.0.0, prosemirror-view@^1.1.0, prosemirror-view@^1.27.0, prosemirror-view@^1.31.0, prosemirror-view@^1.33.8, prosemirror-view@^1.38.1, prosemirror-view@^1.39.1:
  version "1.40.1"
  dependencies:
    prosemirror-model "^1.20.0"
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.1.0"

proxy-from-env@^1.1.0:
  version "1.1.0"

psl@^1.1.33:
  version "1.15.0"
  dependencies:
    punycode "^2.3.1"

punycode.js@^2.3.1:
  version "2.3.1"

punycode@^2.1.0, punycode@^2.1.1, punycode@^2.3.1:
  version "2.3.1"

pure-rand@^7.0.0:
  version "7.0.1"

query-string@^7.1.3:
  version "7.1.3"
  dependencies:
    decode-uri-component "^0.2.2"
    filter-obj "^1.1.0"
    split-on-first "^1.0.0"
    strict-uri-encode "^2.0.0"

querystringify@^2.1.1:
  version "2.2.0"

queue-microtask@^1.2.2:
  version "1.2.3"

radix-ui@^1.1.3:
  version "1.4.2"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-accessible-icon" "1.1.7"
    "@radix-ui/react-accordion" "1.2.11"
    "@radix-ui/react-alert-dialog" "1.1.14"
    "@radix-ui/react-arrow" "1.1.7"
    "@radix-ui/react-aspect-ratio" "1.1.7"
    "@radix-ui/react-avatar" "1.1.10"
    "@radix-ui/react-checkbox" "1.3.2"
    "@radix-ui/react-collapsible" "1.1.11"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-context-menu" "2.2.15"
    "@radix-ui/react-dialog" "1.1.14"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-dropdown-menu" "2.1.15"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-form" "0.1.7"
    "@radix-ui/react-hover-card" "1.1.14"
    "@radix-ui/react-label" "2.1.7"
    "@radix-ui/react-menu" "2.1.15"
    "@radix-ui/react-menubar" "1.1.15"
    "@radix-ui/react-navigation-menu" "1.2.13"
    "@radix-ui/react-one-time-password-field" "0.1.7"
    "@radix-ui/react-password-toggle-field" "0.1.2"
    "@radix-ui/react-popover" "1.1.14"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-progress" "1.1.7"
    "@radix-ui/react-radio-group" "1.3.7"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-scroll-area" "1.2.9"
    "@radix-ui/react-select" "2.2.5"
    "@radix-ui/react-separator" "1.1.7"
    "@radix-ui/react-slider" "1.3.5"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-switch" "1.2.5"
    "@radix-ui/react-tabs" "1.1.12"
    "@radix-ui/react-toast" "1.2.14"
    "@radix-ui/react-toggle" "1.1.9"
    "@radix-ui/react-toggle-group" "1.1.10"
    "@radix-ui/react-toolbar" "1.1.10"
    "@radix-ui/react-tooltip" "1.2.7"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-effect-event" "0.0.2"
    "@radix-ui/react-use-escape-keydown" "1.1.1"
    "@radix-ui/react-use-is-hydrated" "0.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.3"

"react-dom@^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc", "react-dom@^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^17.0.2 || ^18 || ^19", "react-dom@^18 || ^19 || ^19.0.0-rc", "react-dom@^18.0.0 || ^19.0.0", "react-dom@^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom@^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0", react-dom@^19.0.0, react-dom@>=16.6.0, react-dom@>=16.8.0:
  version "19.1.0"
  dependencies:
    scheduler "^0.26.0"

react-hook-form@^7.0.0, react-hook-form@^7.54.2:
  version "7.59.0"

react-is@^16.13.1:
  version "16.13.1"

react-is@^17.0.1:
  version "17.0.2"

react-is@^18.3.1:
  version "18.3.1"

react-remove-scroll-bar@^2.3.7:
  version "2.3.8"
  dependencies:
    react-style-singleton "^2.2.2"
    tslib "^2.0.0"

react-remove-scroll@^2.6.3:
  version "2.7.1"
  dependencies:
    react-remove-scroll-bar "^2.3.7"
    react-style-singleton "^2.2.3"
    tslib "^2.1.0"
    use-callback-ref "^1.3.3"
    use-sidecar "^1.1.3"

react-smooth@^4.0.4:
  version "4.0.4"
  dependencies:
    fast-equals "^5.0.1"
    prop-types "^15.8.1"
    react-transition-group "^4.4.5"

react-style-singleton@^2.2.2, react-style-singleton@^2.2.3:
  version "2.2.3"
  dependencies:
    get-nonce "^1.0.0"
    tslib "^2.0.0"

react-transition-group@^4.4.5:
  version "4.4.5"
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react@*, "react@^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc", "react@^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react@^16.8.0 || ^17 || ^18 || ^19", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^17.0.0 || ^18.0.0 || ^19.0.0", "react@^17.0.2 || ^18 || ^19", "react@^18 || ^19", "react@^18 || ^19 || ^19.0.0-rc", "react@^18.0.0 || ^19.0.0", "react@^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0", react@^19.0.0, react@^19.1.0, "react@>= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0", react@>=16.6.0, react@>=16.8.0, react@>=18.0.0:
  version "19.1.0"

readable-stream@^3.4.0, readable-stream@3:
  version "3.6.2"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

recharts-scale@^0.4.4:
  version "0.4.5"
  dependencies:
    decimal.js-light "^2.4.1"

recharts@^2.15.3:
  version "2.15.4"
  dependencies:
    clsx "^2.0.0"
    eventemitter3 "^4.0.1"
    lodash "^4.17.21"
    react-is "^18.3.1"
    react-smooth "^4.0.4"
    recharts-scale "^0.4.4"
    tiny-invariant "^1.3.1"
    victory-vendor "^36.6.8"

redent@^3.0.0:
  version "3.0.0"
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

reflect-metadata@^0.2.2:
  version "0.2.2"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.10"
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.1"
    which-builtin-type "^1.2.1"

regexp.prototype.flags@^1.5.3, regexp.prototype.flags@^1.5.4:
  version "1.5.4"
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

require-directory@^2.1.1:
  version "2.1.1"

requires-port@^1.0.0:
  version "1.0.0"

resolve-cwd@^3.0.0:
  version "3.0.0"
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"

resolve-from@^5.0.0:
  version "5.0.0"

resolve-pkg-maps@^1.0.0:
  version "1.0.0"

resolve@^1.22.4:
  version "1.22.10"
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.1.0"

rope-sequence@^1.3.0:
  version "1.3.4"

rrweb-cssom@^0.8.0:
  version "0.8.0"

run-async@^4.0.4:
  version "4.0.4"
  dependencies:
    oxlint "^1.2.0"
    prettier "^3.5.3"

run-parallel@^1.1.9:
  version "1.2.0"
  dependencies:
    queue-microtask "^1.2.2"

rxjs@^7.2.0, rxjs@^7.8.2:
  version "7.8.2"
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.1.3:
  version "1.1.3"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@^5.0.1, safe-buffer@~5.2.0:
  version "5.2.1"

safe-push-apply@^1.0.0:
  version "1.0.0"
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.0.3, safe-regex-test@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"

sax@>=0.6.0:
  version "1.4.1"

saxes@^6.0.0:
  version "6.0.0"
  dependencies:
    xmlchars "^2.2.0"

scheduler@^0.26.0:
  version "0.26.0"

semver@^6.3.1:
  version "6.3.1"

semver@^7.5.3, semver@^7.5.4, semver@^7.6.0, semver@^7.7.1, semver@^7.7.2:
  version "7.7.2"

sendgrid-rest@^2.3.0:
  version "2.6.1"
  dependencies:
    core-js "^3.5.0"
    dotenv "^4.0.0"

sendgrid@*:
  version "5.2.3"
  dependencies:
    async.ensureasync "^0.5.2"
    async.queue "^0.5.2"
    bottleneck "^1.12.0"
    debug "^2.2.0"
    lodash.chunk "^4.2.0"
    mailparser "^0.6.1"
    sendgrid-rest "^2.3.0"

set-function-length@^1.2.2:
  version "1.2.2"
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-proto@^1.0.0:
  version "1.0.0"
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

sharp@^0.34.3:
  version "0.34.3"
  dependencies:
    color "^4.2.3"
    detect-libc "^2.0.4"
    semver "^7.7.2"
  optionalDependencies:
    "@img/sharp-darwin-arm64" "0.34.3"
    "@img/sharp-darwin-x64" "0.34.3"
    "@img/sharp-libvips-darwin-arm64" "1.2.0"
    "@img/sharp-libvips-darwin-x64" "1.2.0"
    "@img/sharp-libvips-linux-arm" "1.2.0"
    "@img/sharp-libvips-linux-arm64" "1.2.0"
    "@img/sharp-libvips-linux-ppc64" "1.2.0"
    "@img/sharp-libvips-linux-s390x" "1.2.0"
    "@img/sharp-libvips-linux-x64" "1.2.0"
    "@img/sharp-libvips-linuxmusl-arm64" "1.2.0"
    "@img/sharp-libvips-linuxmusl-x64" "1.2.0"
    "@img/sharp-linux-arm" "0.34.3"
    "@img/sharp-linux-arm64" "0.34.3"
    "@img/sharp-linux-ppc64" "0.34.3"
    "@img/sharp-linux-s390x" "0.34.3"
    "@img/sharp-linux-x64" "0.34.3"
    "@img/sharp-linuxmusl-arm64" "0.34.3"
    "@img/sharp-linuxmusl-x64" "0.34.3"
    "@img/sharp-wasm32" "0.34.3"
    "@img/sharp-win32-arm64" "0.34.3"
    "@img/sharp-win32-ia32" "0.34.3"
    "@img/sharp-win32-x64" "0.34.3"

shebang-command@^2.0.0:
  version "2.0.0"
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"

side-channel-list@^1.0.0:
  version "1.0.0"
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.1.0:
  version "1.1.0"
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.3:
  version "3.0.7"

signal-exit@^4.0.1, signal-exit@^4.1.0:
  version "4.1.0"

simple-swizzle@^0.2.2:
  version "0.2.2"
  dependencies:
    is-arrayish "^0.3.1"

slash@^3.0.0:
  version "3.0.0"

sonner@^2.0.1:
  version "2.0.5"

source-map-js@^1.0.2, source-map-js@^1.2.1:
  version "1.2.1"

source-map-support@0.5.13:
  version "0.5.13"
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0:
  version "0.6.1"

split-on-first@^1.0.0:
  version "1.1.0"

sprintf-js@~1.0.2:
  version "1.0.3"

ssf@~0.11.2:
  version "0.11.2"
  dependencies:
    frac "~1.1.2"

stable-hash@^0.0.5:
  version "0.0.5"

stack-utils@^2.0.6:
  version "2.0.6"
  dependencies:
    escape-string-regexp "^2.0.0"

statuses@^2.0.1:
  version "2.0.2"

stop-iteration-iterator@^1.1.0:
  version "1.1.0"
  dependencies:
    es-errors "^1.3.0"
    internal-slot "^1.1.0"

stream-chain@^2.2.5:
  version "2.2.5"

stream-json@^1.8.0:
  version "1.9.1"
  dependencies:
    stream-chain "^2.2.5"

strict-event-emitter@^0.5.1:
  version "0.5.1"

strict-uri-encode@^2.0.0:
  version "2.0.0"

string_decoder@^1.1.1:
  version "1.3.0"
  dependencies:
    safe-buffer "~5.2.0"

string-length@^4.0.2:
  version "4.0.2"
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.includes@^2.0.1:
  version "2.0.1"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"

string.prototype.matchall@^4.0.12:
  version "4.0.12"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    regexp.prototype.flags "^1.5.3"
    set-function-name "^2.0.2"
    side-channel "^1.1.0"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.10:
  version "1.2.10"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.9:
  version "1.0.9"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"

strip-bom@^4.0.0:
  version "4.0.0"

strip-final-newline@^2.0.0:
  version "2.0.0"

strip-indent@^3.0.0:
  version "3.0.0"
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^3.1.1:
  version "3.1.1"

strnum@^1.1.1:
  version "1.1.2"

strnum@^2.1.0:
  version "2.1.1"

styled-jsx@5.1.6:
  version "5.1.6"
  dependencies:
    client-only "0.0.1"

supports-color@^7.1.0:
  version "7.2.0"
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.1.1:
  version "8.1.1"
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"

symbol-tree@^3.2.4:
  version "3.2.4"

synckit@^0.11.8:
  version "0.11.11"
  dependencies:
    "@pkgr/core" "^0.2.9"

tailwind-merge@^3.0.2:
  version "3.3.1"

tailwindcss-animate@^1.0.7:
  version "1.0.7"

tailwindcss@^4, "tailwindcss@>=3.0.0 || insiders", tailwindcss@4.1.11:
  version "4.1.11"

tapable@^2.2.0:
  version "2.2.2"

tar@^7.4.3:
  version "7.4.3"
  dependencies:
    "@isaacs/fs-minipass" "^4.0.0"
    chownr "^3.0.0"
    minipass "^7.1.2"
    minizlib "^3.0.1"
    mkdirp "^3.0.1"
    yallist "^5.0.0"

test-exclude@^6.0.0:
  version "6.0.0"
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

through2@^4.0.2:
  version "4.0.2"
  dependencies:
    readable-stream "3"

tiny-invariant@^1.3.1:
  version "1.3.3"

tinyglobby@^0.2.13:
  version "0.2.14"
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

tldts-core@^6.1.86:
  version "6.1.86"

tldts@^6.1.32:
  version "6.1.86"
  dependencies:
    tldts-core "^6.1.86"

tmp@^0.0.33:
  version "0.0.33"
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.5:
  version "1.0.5"

to-regex-range@^5.0.1:
  version "5.0.1"
  dependencies:
    is-number "^7.0.0"

tough-cookie@^4.1.4:
  version "4.1.4"
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tough-cookie@^5.1.1:
  version "5.1.2"
  dependencies:
    tldts "^6.1.32"

tr46@^5.1.0:
  version "5.1.1"
  dependencies:
    punycode "^2.3.1"

ts-api-utils@^2.1.0:
  version "2.1.0"

ts-jest@^29.4.0:
  version "29.4.0"
  dependencies:
    bs-logger "^0.2.6"
    ejs "^3.1.10"
    fast-json-stable-stringify "^2.1.0"
    json5 "^2.2.3"
    lodash.memoize "^4.1.2"
    make-error "^1.3.6"
    semver "^7.7.2"
    type-fest "^4.41.0"
    yargs-parser "^21.1.1"

tsconfig-paths@^3.15.0:
  version "3.15.0"
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^2.0.0, tslib@^2.1.0, tslib@^2.4.0, tslib@^2.6.2, tslib@^2.8.0:
  version "2.8.1"

tsx@^4.20.3:
  version "4.20.3"
  dependencies:
    esbuild "~0.25.0"
    get-tsconfig "^4.7.5"
  optionalDependencies:
    fsevents "~2.3.3"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  dependencies:
    prelude-ls "^1.2.1"

type-detect@4.0.8:
  version "4.0.8"

type-fest@^0.21.3:
  version "0.21.3"

type-fest@^4.26.1:
  version "4.41.0"

type-fest@^4.41.0:
  version "4.41.0"

typed-array-buffer@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typescript-eslint@^8.28.0:
  version "8.35.1"
  dependencies:
    "@typescript-eslint/eslint-plugin" "8.35.1"
    "@typescript-eslint/parser" "8.35.1"
    "@typescript-eslint/utils" "8.35.1"

typescript@^5, "typescript@>= 4.8.x", typescript@>=3.3.1, "typescript@>=4.3 <6", typescript@>=4.8.4, "typescript@>=4.8.4 <5.9.0", typescript@>=5.1.0:
  version "5.8.3"

uc.micro@^2.0.0, uc.micro@^2.1.0:
  version "2.1.0"

unbox-primitive@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~6.21.0:
  version "6.21.0"

undici-types@~7.8.0:
  version "7.8.0"

universalify@^0.2.0:
  version "0.2.0"

unrs-resolver@^1.6.2, unrs-resolver@^1.7.11:
  version "1.9.2"
  dependencies:
    napi-postinstall "^0.2.4"
  optionalDependencies:
    "@unrs/resolver-binding-android-arm-eabi" "1.9.2"
    "@unrs/resolver-binding-android-arm64" "1.9.2"
    "@unrs/resolver-binding-darwin-arm64" "1.9.2"
    "@unrs/resolver-binding-darwin-x64" "1.9.2"
    "@unrs/resolver-binding-freebsd-x64" "1.9.2"
    "@unrs/resolver-binding-linux-arm-gnueabihf" "1.9.2"
    "@unrs/resolver-binding-linux-arm-musleabihf" "1.9.2"
    "@unrs/resolver-binding-linux-arm64-gnu" "1.9.2"
    "@unrs/resolver-binding-linux-arm64-musl" "1.9.2"
    "@unrs/resolver-binding-linux-ppc64-gnu" "1.9.2"
    "@unrs/resolver-binding-linux-riscv64-gnu" "1.9.2"
    "@unrs/resolver-binding-linux-riscv64-musl" "1.9.2"
    "@unrs/resolver-binding-linux-s390x-gnu" "1.9.2"
    "@unrs/resolver-binding-linux-x64-gnu" "1.9.2"
    "@unrs/resolver-binding-linux-x64-musl" "1.9.2"
    "@unrs/resolver-binding-wasm32-wasi" "1.9.2"
    "@unrs/resolver-binding-win32-arm64-msvc" "1.9.2"
    "@unrs/resolver-binding-win32-ia32-msvc" "1.9.2"
    "@unrs/resolver-binding-win32-x64-msvc" "1.9.2"

update-browserslist-db@^1.1.3:
  version "1.1.3"
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  dependencies:
    punycode "^2.1.0"

url-parse@^1.5.3:
  version "1.5.10"
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

use-callback-ref@^1.3.3:
  version "1.3.3"
  dependencies:
    tslib "^2.0.0"

use-debounce@^10.0.5:
  version "10.0.5"

use-sidecar@^1.1.3:
  version "1.1.3"
  dependencies:
    detect-node-es "^1.1.0"
    tslib "^2.0.0"

use-sync-external-store@^1.4.0, use-sync-external-store@^1.5.0, use-sync-external-store@>=1.2.0:
  version "1.5.0"

util-deprecate@^1.0.1:
  version "1.0.2"

util@^0.12.3:
  version "0.12.5"
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

uue@^3.1.0:
  version "3.1.2"
  dependencies:
    escape-string-regexp "~1.0.5"
    extend "~3.0.0"

uuid@^11.1.0:
  version "11.1.0"

uuid@^8.3.2:
  version "8.3.2"

uuid@^9.0.1:
  version "9.0.1"

v8-to-istanbul@^9.0.1:
  version "9.3.0"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^2.0.0"

victory-vendor@^36.6.8:
  version "36.9.2"
  dependencies:
    "@types/d3-array" "^3.0.3"
    "@types/d3-ease" "^3.0.0"
    "@types/d3-interpolate" "^3.0.1"
    "@types/d3-scale" "^4.0.2"
    "@types/d3-shape" "^3.1.0"
    "@types/d3-time" "^3.0.0"
    "@types/d3-timer" "^3.0.0"
    d3-array "^3.1.6"
    d3-ease "^3.0.1"
    d3-interpolate "^3.0.1"
    d3-scale "^4.0.2"
    d3-shape "^3.1.0"
    d3-time "^3.0.0"
    d3-timer "^3.0.1"

w3c-keyname@^2.2.0:
  version "2.2.8"

w3c-xmlserializer@^5.0.0:
  version "5.0.0"
  dependencies:
    xml-name-validator "^5.0.0"

walker@^1.0.8:
  version "1.0.8"
  dependencies:
    makeerror "1.0.12"

web-encoding@^1.1.5:
  version "1.1.5"
  dependencies:
    util "^0.12.3"
  optionalDependencies:
    "@zxing/text-encoding" "0.9.0"

web-streams-polyfill@^3.0.3:
  version "3.3.3"

webidl-conversions@^7.0.0:
  version "7.0.0"

whatwg-encoding@^3.1.1:
  version "3.1.1"
  dependencies:
    iconv-lite "0.6.3"

whatwg-fetch@^3.6.20:
  version "3.6.20"

whatwg-mimetype@^4.0.0:
  version "4.0.0"

whatwg-url@^14.0.0, whatwg-url@^14.1.1:
  version "14.2.0"
  dependencies:
    tr46 "^5.1.0"
    webidl-conversions "^7.0.0"

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.16, which-typed-array@^1.1.19, which-typed-array@^1.1.2:
  version "1.1.19"
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  dependencies:
    isexe "^2.0.0"

wmf@~1.0.1:
  version "1.0.2"

word-wrap@^1.2.5:
  version "1.2.5"

word@~0.3.0:
  version "0.3.0"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"

write-file-atomic@^5.0.1:
  version "5.0.1"
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^4.0.1"

ws@^8.18.0:
  version "8.18.3"

xlsx@^0.18.5:
  version "0.18.5"
  dependencies:
    adler-32 "~1.3.0"
    cfb "~1.2.1"
    codepage "~1.15.0"
    crc-32 "~1.2.1"
    ssf "~0.11.2"
    wmf "~1.0.1"
    word "~0.3.0"

xml-name-validator@^5.0.0:
  version "5.0.0"

"xml2js@^0.5.0 || ^0.6.2":
  version "0.6.2"
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@~11.0.0:
  version "11.0.1"

xmlchars@^2.2.0:
  version "2.2.0"

y18n@^5.0.5:
  version "5.0.8"

yallist@^3.0.2:
  version "3.1.1"

yallist@^4.0.0:
  version "4.0.0"

yallist@^5.0.0:
  version "5.0.0"

yargs-parser@^21.1.1:
  version "21.1.1"

yargs@^17.7.2:
  version "17.7.2"
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yocto-queue@^0.1.0:
  version "0.1.0"

yoctocolors-cjs@^2.1.2:
  version "2.1.2"

zod@^3.24.2:
  version "3.25.67"

zustand@^5.0.6:
  version "5.0.6"
